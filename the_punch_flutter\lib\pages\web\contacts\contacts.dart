import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/search_text_field.dart';
import '../my_scaffold.dart';
import 'contact_mixin.dart';
import '../../../widgets/tables_global.dart';

class ContactsPage extends StatelessWidget {
  const ContactsPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalization.of(context).contacts,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.centerLeft,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _TableHeader(),
              Expanded(
                // Keep only Expanded here for table stability
                child: _ContactsTable(),
              ),
            ],
          ),
        ),
      );
}

class _TableHeader extends StatefulWidget {
  @override
  _TableHeaderState createState() => _TableHeaderState();
}

class _TableHeaderState extends State<_TableHeader> {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) => Card(
        color: Colors.transparent,
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          child: Flex(
            direction: isMobile ? Axis.vertical : Axis.horizontal,
            children: [
              ConstrainedBox(
                constraints:
                    BoxConstraints(maxWidth: isMobile ? double.infinity : 300),
                child: SearchTextField(
                  notifier: viewModel.searchNotifier,
                ),
              ),
              SizedBox(height: isMobile ? 10 : 0, width: isMobile ? 0 : 20),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: ActiveToggle(viewModel.activeNotifier),
                ),
              ),
              SizedBox(height: isMobile ? 10 : 0, width: isMobile ? 0 : 20),
              if (PermissionsState().editContacts)
                Align(
                  alignment: Alignment.centerLeft,
                  child: ElevatedButton.icon(
                    onPressed: () => context.go('/contacts/edit?anything=1'),
                    icon: const Icon(Icons.add),
                    label: Text(AppLocalization.of(context).addContact),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
                      padding: EdgeInsets.symmetric(
                        vertical: isMobile ? 12 : 8,
                        horizontal: isMobile ? 16 : 12,
                      ),
                      textStyle: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ContactsTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final rows = _getRows(viewModel, context);

          return CustomTable(
            columns: _getColumns(context),
            rows: rows,
            mobileTableTitle: 'Contacts Tables',
            onRowClick: (DataRow row) async {
              final contactName = (row.cells[0].child as Text).data;
              final selectedContact = viewModel.contacts
                  .firstWhere((contact) => contact.name == contactName);
              await context.pushNamed(
                '/contacts/view',
                queryParameters: {'id': selectedContact.id},
              );
            },
            headerFontSize: 14,
            cellFontSize: 13,
          );
        },
      );

  List<DataColumn> _getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).contactName)),
        DataColumn(label: Text(AppLocalization.of(context).location)),
        DataColumn(label: Text(AppLocalization.of(context).phone)),
        DataColumn(label: Text(AppLocalization.of(context).emailAddress)),
        const DataColumn(label: Expanded(child: Center(child: Text('Active')))),
        const DataColumn(label: Expanded(child: Center(child: Text('Actions')))),
      ];

  List<DataRow> _getRows(_ViewModel viewModel, BuildContext context) {
    if (!viewModel.isRefreshed) return [];

    Iterable<User> contacts = viewModel.contacts;

    // Apply active state filter
    switch (viewModel.activeNotifier.value) {
      case ActiveToggleState.active:
        contacts = contacts.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        contacts = contacts.where((e) => !e.isActive);
        break;
      default:
        break;
    }

    // Apply search filter
    final search = viewModel.searchNotifier.value;
    if (search.isNotEmpty) {
      contacts = contacts.where((e) {
        if (e.name.toLowerCase().contains(search.toLowerCase())) return true;
        if (e.phone != null && e.phone!.contains(search)) return true;
        if (e.emailAddress != null &&
            e.emailAddress!.toLowerCase().contains(search)) return true;
        return false;
      });
    }

    return contacts
        .map((contact) => DataRow(
              cells: [
                DataCell(Text(contact.name, style: const TextStyle(fontWeight: FontWeight.bold))),
                DataCell(Text(viewModel.locationsForContact(contact.id) ?? '')),
                DataCell(Text(contact.phone ?? '')),
                DataCell(Text(contact.emailAddress ?? '')),
                            DataCell(Center(child: contact.isActive
                  ? Image.asset('images/custom_location.png', width: 24, height: 24)
                  : const Icon(Symbols.where_to_vote, color: Colors.grey))),
                DataCell(Center(child:
                  ElevatedButton(
                    onPressed: () async {
                      await context.pushNamed('/contacts/view',
                        queryParameters: {'id': contact.id});
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 4),
                      textStyle: const TextStyle(fontSize: 12),
                      foregroundColor: Colors.blue,
                      backgroundColor: Colors.white,
                      side: const BorderSide(color: Colors.blue),
                    ),
                    child: const Text('View'),
                  ),
                )),
              ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, ContactMixin {
  var contacts = <User>[];
  var contactLocationMap = <String, Iterable<Location>>{};
  var activeNotifier = ValueNotifier(ActiveToggleState.active);
  var searchNotifier = ValueNotifier('');

  _ViewModel() {
    addListenables([
      DataModel().userModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
    ]);

    searchNotifier.addListener(notifyListeners);
    activeNotifier.addListener(notifyListeners);

    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    contacts = (await DataModel().userModel.allContacts).toList();
    final userIds = contacts.map((e) => e.id);
    final locationContacts =
        await DataModel().locationContactModel.getByUserIds(userIds);
    final locationIds = locationContacts.map((e) => e.locationId).toSet();
    final locations =
        (await DataModel().locationModel.getByIds(locationIds)).toList();
    contactLocationMap = {
      for (final e in userIds)
        e: locationContacts
            .where((locationContact) => locationContact.userId == e)
            .map((locationContact) =>
                locations.firstWhere((e) => locationContact.locationId == e.id))
    };
    notifyListeners();
  }

  String? locationsForContact(String id) =>
      contactLocationMap[id]?.map((e) => e.name).join('\n');
}
