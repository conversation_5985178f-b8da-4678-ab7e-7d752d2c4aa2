// ignore_for_file: prefer_expression_function_bodies

import 'dart:async';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../api/api_model.dart';
import '../../../api/requests/fetch_travel_pings_request.dart';
import '../../../api/sync_model.dart';
import '../../../dataModel/data/alert.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/data/travel_pings.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../misc/app_localization.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/bitmap_descriptor_extensions.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/tables_global.dart';
import '../../view_model_mixin.dart';
import '../dashboard_widgets/status_widget/StatusComponent.dart';
import '../my_scaffold.dart';
import 'widgets/total_counts.dart';

// Custom Scroll Behavior
class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
          BuildContext context, Widget child, AxisDirection axisDirection) =>
      child;
}

// TrianglePainter class
class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color.fromARGB(255, 255, 255, 255)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, size.height / 2)
      ..lineTo(size.width, 0)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Home Page
class MapPage extends StatefulWidget {
  const MapPage({super.key});

  @override
  _MapPageState createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  String _mapStyle = '';
  bool employeeSelected = false;
  bool secondaryMenu = false;

  @override
  void initState() {
    super.initState();
    _loadMapStyle();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 1024;
    final double scaleFactorAdjust = isMobile ? 1.0 : 0.85;
    final double desktopFontSize = isMobile ? 14 : 14;
    final double headerFontSize = 12.0;
    final double cellFontSize = 19.0;

    final double screenWidth = MediaQuery.of(context).size.width;
    final bool needsHorizontalScroll =
        screenWidth <= 800 || (screenWidth >= 1024 && screenWidth <= 1400);
     bool secondaryMenu = false;
    @override
    void initState() {
      super.initState();
      // _loadCustomMarker();
      _loadMapStyle();
    }

    return ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel()..initialize(context),
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) => MyScaffold(
          title: AppLocalization.of(context).home,
          body: Column(
            children: [
              // Map Container - Stationary, outside scroll view
              Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: isMobile ? MediaQuery.of(context).size.width : 500,
                    //  height:MediaQuery.of(context).size.height - 100,
                      child: Column(
                        children: [
                          if(!isMobile)
                            TotalCounts(viewModel: viewModel, year: viewModel.selectedYear ?? DateTime.now().year, month:
                            viewModel.selectedMonth ?? DateFormat('MMMM').format(DateTime.now()), ),
                          const SizedBox(height: 10),
                          Container(
                            height: MediaQuery.of(context).size.height - 100,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: ColorHelper.thePunchAdminButtonBlue()
                                    .withOpacity(.1),
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(35),
                            ),
                            child: Column(
                              children: [
                                StatusComponent(viewModel: viewModel),
                              if(isMobile)
                              Container(
                           // constraints:
                              //  BoxConstraints(minWidth: 600, maxWidth: 800),
                            child: Column(children: [
                              Container(
                                // constraints: BoxConstraints(
                                //     minWidth: 600, maxWidth: 1000),
                                //padding: EdgeInsets.all(10),
                                height: isMobile? 400: (viewModel.employeeSelected ? 450 : 650),
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 5, vertical: 0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  //     color: Colors.black.withOpacity(0.1),
                                  //     blurRadius: 10,
                                  //     offset: const Offset(0, 5),
                                  //   ),
                                  // ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: Stack(
                                    children: [
                                      Consumer<_ViewModel>(
                                        builder: (context, viewModel, child) =>
                                            GoogleMap(
                                          initialCameraPosition:
                                              const CameraPosition(
                                            target: LatLng(0, 0),
                                            zoom: 12,
                                          ),
                                          onMapCreated: (GoogleMapController
                                              controller) async {
                                            if (!viewModel
                                                .mapController.isCompleted) {
                                              viewModel.mapController
                                                  .complete(controller);
                                            }
                                            controller.setMapStyle(_mapStyle);

                                            await Future.delayed(
                                                const Duration(seconds: 1));
                                            await viewModel.initializeMarkers();
                                          },
                                          circles: viewModel.circles,
                                          markers: viewModel.markers,
                                          onTap: (LatLng position) =>
                                              viewModel.onMapTap(),
                                          mapType: MapType.normal,
                                          myLocationEnabled: true,
                                          zoomControlsEnabled: false,
                                        ),
                                      ),
                                      if (viewModel.selectedPunchCard != null &&
                                          viewModel.selectedMarkerPosition !=
                                              null)
                                        FutureBuilder<ScreenCoordinate?>(
                                          future: viewModel
                                              .getMarkerScreenPosition(),
                                          builder: (context, snapshot) {
                                            if (!snapshot.hasData ||
                                                snapshot.data == null) {
                                              return const SizedBox.shrink();
                                            }
                                            final screenPosition =
                                                snapshot.data!;

                                            // Fixed size for your pop-up card
                                            const double cardWidth = 200;
                                            const double cardHeight = 195;
                                            const double arrowHeight = 10;

                                            return Positioned(
                                              left: 0,
                                                  
                                              top: 0,
                                              child: SizedBox(
                                                width: cardWidth,
                                                height: cardHeight,
                                                // Force text scale factor to something smaller than 1.0
                                                child: MediaQuery(
                                                  data: MediaQuery.of(context)
                                                      .copyWith(
                                                    textScaleFactor:
                                                        0.9, // <--- MAKE TEXT APPEAR SMALLER
                                                  ),
                                                  child: Stack(
                                                    alignment:
                                                        Alignment.topCenter,
                                                    children: [
                                                      Card(
                                                        color: Colors.white,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(20),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(10.0),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              // Title & Close Button
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Text(
                                                                    viewModel.selectedPunchCard?.id ==
                                                                            'scheduled'
                                                                        ? 'Scheduled'
                                                                        : viewModel.selectedPunchCard?.clockedOut ==
                                                                                null
                                                                            ? 'Live'
                                                                            : 'Completed',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          14,
                                                                      color: (viewModel.selectedPunchCard?.clockedOut ==
                                                                              null
                                                                          ? Colors
                                                                              .green
                                                                          : viewModel.selectedPunchCard?.id == 'scheduled'
                                                                              ? Colors.grey
                                                                              : Colors.black),
                                                                    ),
                                                                  ),
                                                                  IconButton(
                                                                    icon: const Icon(
                                                                        Icons
                                                                            .close,
                                                                        size:
                                                                            14), // smaller icon
                                                                    onPressed:
                                                                        viewModel
                                                                            .clearSelectedPunchCard,
                                                                  ),
                                                                ],
                                                              ),
                                                              // Alerts row
                                                              Row(
                                                                children: viewModel
                                                                    .alerts
                                                                    .where((alert) =>
                                                                        alert
                                                                            .punchCardId ==
                                                                        viewModel
                                                                            .selectedPunchCard
                                                                            ?.id)
                                                                    .where((alert) =>
                                                                        alert
                                                                            .alertTypeId !=
                                                                        Alert
                                                                            .reconnectedGeofenceId)
                                                                    .map((alert) =>
                                                                        viewModel.alertColors[alert
                                                                            .alertTypeId] ??
                                                                        Colors
                                                                            .transparent)
                                                                    .toSet()
                                                                    .map((color) =>
                                                                        Container(
                                                                          width:
                                                                              10, // smaller
                                                                          height:
                                                                              10,
                                                                          margin: const EdgeInsets
                                                                              .symmetric(
                                                                              horizontal: 2),
                                                                          decoration:
                                                                              BoxDecoration(
                                                                            color:
                                                                                color,
                                                                            borderRadius:
                                                                                BorderRadius.circular(5), // Add border radius
                                                                          ),
                                                                        ))
                                                                    .toList(),
                                                              ),
                                                              const SizedBox(
                                                                  height: 5),
                                                              // Punch Card Info
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Employee ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          12, // smaller
                                                                    ),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 4),
                                                                  Text(
                                                                    () {
                                                                      final fullName = '${viewModel.userMap[viewModel.selectedPunchCard?.userId]?.firstName ?? 'Unknown'} '
                                                                          '${viewModel.userMap[viewModel.selectedPunchCard?.userId]?.lastName ?? ''}';
                                                                      return fullName.length > 12
                                                                          ? '${fullName.substring(0, 12)}...'
                                                                          : fullName;
                                                                    }(),
                                                                    style:
                                                                        const TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12, // smaller
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Punch In ',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.clockedIn.toFormattedDateTime(Localizations.localeOf(context)) ?? '---'}',
                                                                    style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Punch Out ',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.clockedOut?.toFormattedDateTime(Localizations.localeOf(context)) ?? '---'}',
                                                                    style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Duration',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.duration != null ? "${viewModel.selectedPunchCard!.duration!.inHours}h ${viewModel.selectedPunchCard!.duration!.inMinutes.remainder(60)}m" : '---'}',
                                                                    style:
                                                                        const TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              const SizedBox(
                                                                  height: 8),
                                                              // View Full Button
                                                              Row(
                                                                children: [
                                                                  SizedBox(
                                                                    width: 125,
                                                                    child:
                                                                        ElevatedButton(
                                                                      onPressed:
                                                                          () async {
                                                                        // if (viewModel.selectedPunchCard?.id ==
                                                                        //     'scheduled') {
                                                                        //   await context
                                                                        //       .pushNamed('/schedules');
                                                                        // } else {
                                                                          await context
                                                                              .pushNamed(
                                                                            '/punchCards/view',
                                                                            queryParameters: {
                                                                              'id': viewModel.selectedPunchCard?.punchCardLinkId ?? '',
                                                                            },
                                                                          );
                                                                        //}
                                                                      },
                                                                      style:
                                                                          ButtonStyle(
                                                                        // Force the cursor to show a pointer/hand on hover:
                                                                        mouseCursor:
                                                                            MaterialStateProperty.all(SystemMouseCursors.click),
                                                                        backgroundColor:
                                                                            MaterialStateProperty.all(ColorHelper.thePunchAdminButtonBlue()),
                                                                      ),
                                                                      child:
                                                                          const Text(
                                                                        'View Full Card',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12, // smaller
                                                                          color:
                                                                              Colors.white,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      // The triangle pointing down
                                                      Positioned(
                                                        bottom: 100,
                                                        left:
                                                            0, // Adjust this value to move the widget to the left
                                                        child: CustomPaint(
                                                          size: const Size(
                                                              10, 10),
                                                          painter:
                                                              TrianglePainter(),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      Positioned(
                                        top: 10,
                                        right: 10,
                                        child: Column(
                                          children: [
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag1',
                                              mini: true,
                                              onPressed: () async {
                                                final controller =
                                                    await viewModel
                                                        .mapController.future;
                                                controller.animateCamera(
                                                    CameraUpdate.zoomIn());
                                              },
                                              child: const Icon(Icons.zoom_in),
                                            ),
                                            const SizedBox(height: 10),
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag2',
                                              mini: true,
                                              onPressed: () async {
                                                final controller =
                                                    await viewModel
                                                        .mapController.future;
                                                controller.animateCamera(
                                                    CameraUpdate.zoomOut());
                                              },
                                              child: const Icon(Icons.zoom_out),
                                            ),
                                            const SizedBox(height: 10),
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag8',
                                              mini: true,
                                              onPressed: () {
                                                // Open filter options
                                                showModalBottomSheet(
                                                  context: context,
                                                  shape:
                                                      const RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.vertical(
                                                      top: Radius.circular(20),
                                                    ),
                                                  ),
                                                  builder: (context) =>
                                                      FilterOptionsSheet(
                                                    viewModel: viewModel,
                                                  ),
                                                );
                                              },
                                              child:
                                                  const Icon(Icons.filter_list),
                                              tooltip: 'Filter Data',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (viewModel.employeeSelected)
                                const SizedBox(height: 10),

                            ])),
                        //     )
                              if (viewModel.employeeSelected)
                                _buildSecondExpandedWidget(
                                    context, viewModel, isMobile),
                                    needsHorizontalScroll
                                        ? (  !viewModel.employeeSelected ? Expanded(  child: 
                                        SingleChildScrollView(
                                          scrollDirection: Axis.vertical,
                                          child:   _buildCustomTable(
                                                context,
                                                viewModel,
                                                headerFontSize,
                                                cellFontSize),
                                          )
                                        ): SizedBox.shrink()  )
                                        : _buildCustomTable(context, viewModel,
                                            headerFontSize, cellFontSize),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                  
                        // Container(
                        //   constraints: BoxConstraints(maxWidth: 1000,minWidth: 500),
                        //   child:
                        if(!isMobile)
                         Expanded(
                        flex: 2,
                         child:
                        Container(
                           // constraints:
                              //  BoxConstraints(minWidth: 600, maxWidth: 800),
                            child: Column(children: [
                              Container(
                                // constraints: BoxConstraints(
                                //     minWidth: 600, maxWidth: 1000),
                                //padding: EdgeInsets.all(10),
                                height: viewModel.employeeSelected ? 450 : 650,
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 5, vertical: 0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  //     color: Colors.black.withOpacity(0.1),
                                  //     blurRadius: 10,
                                  //     offset: const Offset(0, 5),
                                  //   ),
                                  // ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: Stack(
                                    children: [
                                      Consumer<_ViewModel>(
                                        builder: (context, viewModel, child) =>
                                            GoogleMap(
                                          initialCameraPosition:
                                              const CameraPosition(
                                            target: LatLng(0, 0),
                                            zoom: 12,
                                          ),
                                          onMapCreated: (GoogleMapController
                                              controller) async {
                                            if (!viewModel
                                                .mapController.isCompleted) {
                                              viewModel.mapController
                                                  .complete(controller);
                                            }
                                            controller.setMapStyle(_mapStyle);

                                            await Future.delayed(
                                                const Duration(seconds: 1));
                                            await viewModel.initializeMarkers();
                                          },
                                          circles: viewModel.circles,
                                          markers: viewModel.markers,
                                          onTap: (LatLng position) =>
                                              viewModel.onMapTap(),
                                          mapType: MapType.normal,
                                          myLocationEnabled: true,
                                          zoomControlsEnabled: false,
                                        ),
                                      ),
                                      if (viewModel.selectedPunchCard != null &&
                                          viewModel.selectedMarkerPosition !=
                                              null)
                                        FutureBuilder<ScreenCoordinate?>(
                                          future: viewModel
                                              .getMarkerScreenPosition(),
                                          builder: (context, snapshot) {
                                            if (!snapshot.hasData ||
                                                snapshot.data == null) {
                                              return const SizedBox.shrink();
                                            }
                                            final screenPosition =
                                                snapshot.data!;

                                            // Fixed size for your pop-up card
                                            const double cardWidth = 200;
                                            const double cardHeight = 195;
                                            const double arrowHeight = 10;

                                            return Positioned(
                                              left: 0,
                                                  
                                              top: 0,
                                              child: SizedBox(
                                                width: cardWidth,
                                                height: cardHeight,
                                                // Force text scale factor to something smaller than 1.0
                                                child: MediaQuery(
                                                  data: MediaQuery.of(context)
                                                      .copyWith(
                                                    textScaleFactor:
                                                        0.9, // <--- MAKE TEXT APPEAR SMALLER
                                                  ),
                                                  child: Stack(
                                                    alignment:
                                                        Alignment.topCenter,
                                                    children: [
                                                      Card(
                                                        color: Colors.white,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(20),
                                                        ),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(10.0),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              // Title & Close Button
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Text(
                                                                    viewModel.selectedPunchCard?.id ==
                                                                            'scheduled'
                                                                        ? 'Scheduled'
                                                                        : viewModel.selectedPunchCard?.clockedOut ==
                                                                                null
                                                                            ? 'Live'
                                                                            : 'Completed',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          14,
                                                                      color: (viewModel.selectedPunchCard?.clockedOut ==
                                                                              null
                                                                          ? Colors
                                                                              .green
                                                                          : viewModel.selectedPunchCard?.id == 'scheduled'
                                                                              ? Colors.grey
                                                                              : Colors.black),
                                                                    ),
                                                                  ),
                                                                  IconButton(
                                                                    icon: const Icon(
                                                                        Icons
                                                                            .close,
                                                                        size:
                                                                            14), // smaller icon
                                                                    onPressed:
                                                                        viewModel
                                                                            .clearSelectedPunchCard,
                                                                  ),
                                                                ],
                                                              ),
                                                              // Alerts row
                                                              Row(
                                                                children: viewModel
                                                                    .alerts
                                                                    .where((alert) =>
                                                                        alert
                                                                            .punchCardId ==
                                                                        viewModel
                                                                            .selectedPunchCard
                                                                            ?.id)
                                                                    .where((alert) =>
                                                                        alert
                                                                            .alertTypeId !=
                                                                        Alert
                                                                            .reconnectedGeofenceId)
                                                                    .map((alert) =>
                                                                        viewModel.alertColors[alert
                                                                            .alertTypeId] ??
                                                                        Colors
                                                                            .transparent)
                                                                    .toSet()
                                                                    .map((color) =>
                                                                        Container(
                                                                          width:
                                                                              10, // smaller
                                                                          height:
                                                                              10,
                                                                          margin: const EdgeInsets
                                                                              .symmetric(
                                                                              horizontal: 2),
                                                                          decoration:
                                                                              BoxDecoration(
                                                                            color:
                                                                                color,
                                                                            borderRadius:
                                                                                BorderRadius.circular(5), // Add border radius
                                                                          ),
                                                                        ))
                                                                    .toList(),
                                                              ),
                                                              const SizedBox(
                                                                  height: 5),
                                                              // Punch Card Info
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Employee ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          12, // smaller
                                                                    ),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 4),
                                                                  Text(
                                                                    () {
                                                                      final fullName = '${viewModel.userMap[viewModel.selectedPunchCard?.userId]?.firstName ?? 'Unknown'} '
                                                                          '${viewModel.userMap[viewModel.selectedPunchCard?.userId]?.lastName ?? ''}';
                                                                      return fullName.length > 12
                                                                          ? '${fullName.substring(0, 12)}...'
                                                                          : fullName;
                                                                    }(),
                                                                    style:
                                                                        const TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12, // smaller
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Punch In ',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.clockedIn.toFormattedDateTime(Localizations.localeOf(context)) ?? '---'}',
                                                                    style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Punch Out ',
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.clockedOut?.toFormattedDateTime(Localizations.localeOf(context)) ?? '---'}',
                                                                    style: const TextStyle(
                                                                        fontSize:
                                                                            12,
                                                                        fontWeight:
                                                                            FontWeight.bold),
                                                                  ),
                                                                ],
                                                              ),
                                                              Row(
                                                                children: [
                                                                  const Text(
                                                                    'Duration',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  const Text(
                                                                    ' • ',
                                                                    style:
                                                                        TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${viewModel.selectedPunchCard?.duration != null ? "${viewModel.selectedPunchCard!.duration!.inHours}h ${viewModel.selectedPunchCard!.duration!.inMinutes.remainder(60)}m" : '---'}',
                                                                    style:
                                                                        const TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      fontSize:
                                                                          12,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              const SizedBox(
                                                                  height: 8),
                                                              // View Full Button
                                                              Row(
                                                                children: [
                                                                  SizedBox(
                                                                    width: 125,
                                                                    child:
                                                                        ElevatedButton(
                                                                      onPressed:
                                                                          () async {
                                                                        // if (viewModel.selectedPunchCard?.id ==
                                                                        //     'scheduled') {
                                                                        //   await context
                                                                        //       .pushNamed('/schedules');
                                                                        // } else {
                                                                          await context
                                                                              .pushNamed(
                                                                            '/punchCards/view',
                                                                            queryParameters: {
                                                                              'id': viewModel.selectedPunchCard?.punchCardLinkId ?? '',
                                                                            },
                                                                          );
                                                                        //}
                                                                      },
                                                                      style:
                                                                          ButtonStyle(
                                                                        // Force the cursor to show a pointer/hand on hover:
                                                                        mouseCursor:
                                                                            MaterialStateProperty.all(SystemMouseCursors.click),
                                                                        backgroundColor:
                                                                            MaterialStateProperty.all(ColorHelper.thePunchAdminButtonBlue()),
                                                                      ),
                                                                      child:
                                                                          const Text(
                                                                        'View Full Card',
                                                                        style:
                                                                            TextStyle(
                                                                          fontSize:
                                                                              12, // smaller
                                                                          color:
                                                                              Colors.white,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                      // The triangle pointing down
                                                      Positioned(
                                                        bottom: 100,
                                                        left:
                                                            0, // Adjust this value to move the widget to the left
                                                        child: CustomPaint(
                                                          size: const Size(
                                                              10, 10),
                                                          painter:
                                                              TrianglePainter(),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      Positioned(
                                        top: 10,
                                        right: 10,
                                        child: Column(
                                          children: [
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag1',
                                              mini: true,
                                              onPressed: () async {
                                                final controller =
                                                    await viewModel
                                                        .mapController.future;
                                                controller.animateCamera(
                                                    CameraUpdate.zoomIn());
                                              },
                                              child: const Icon(Icons.zoom_in),
                                            ),
                                            const SizedBox(height: 10),
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag2',
                                              mini: true,
                                              onPressed: () async {
                                                final controller =
                                                    await viewModel
                                                        .mapController.future;
                                                controller.animateCamera(
                                                    CameraUpdate.zoomOut());
                                              },
                                              child: const Icon(Icons.zoom_out),
                                            ),
                                            const SizedBox(height: 10),
                                            FloatingActionButton(
                                              heroTag: 'uniqueTag8',
                                              mini: true,
                                              onPressed: () {
                                                // Open filter options
                                                showModalBottomSheet(
                                                  context: context,
                                                  shape:
                                                      const RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.vertical(
                                                      top: Radius.circular(20),
                                                    ),
                                                  ),
                                                  builder: (context) =>
                                                      FilterOptionsSheet(
                                                    viewModel: viewModel,
                                                  ),
                                                );
                                              },
                                              child:
                                                  const Icon(Icons.filter_list),
                                              tooltip: 'Filter Data',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (viewModel.employeeSelected)
                                const SizedBox(height: 10),
                              if (viewModel.employeeSelected)
                                _buildSecondExpandedWidget(
                                    context, viewModel, isMobile),
                            ])),
                        //     )

                          ) //     )
                      ],
                    ),
                    // Map Container
                    // If mobile (<=1024px), stack them on separate rows
                  ),
                ],
              ),
            ),
          ),
        );
  //    ),
    //);
  }

  Future<void> _loadMapStyle() async {
    try {
      _mapStyle = await rootBundle.loadString('assets/map_style.json');
    } catch (e) {
      print('Error loading map style: $e');
    }
  }


Widget _buildFirstExpandedWidget(
    BuildContext context, _ViewModel viewModel, bool isMobile) {
  // Define necessary variables inside the widget
  final double scaleFactorAdjust = isMobile ? 1.0 : 0.85;
  final double desktopFontSize = isMobile ? 14 : 14;
  final double headerFontSize = 12.0;
  final double cellFontSize = 9.0;

  final double screenWidth = MediaQuery.of(context).size.width;
  final bool needsHorizontalScroll =
      screenWidth <= 800 || (screenWidth >= 1024 && screenWidth <= 1400);
  final bool useColumnLayout =
      screenWidth < 600 || (screenWidth >= 1025 && screenWidth <= 1400);

  BoxDecoration commonDecoration = BoxDecoration(
    color: Colors.white,
    border: Border.all(
      color: ColorHelper.thePunchDesktopLightGray(),
      width: 1,
    ),
    borderRadius: BorderRadius.circular(20),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 12,
        offset: const Offset(0, 6),
      ),
    ],
  );

  return Column(
    children: [
      // Info Container
      Container(
        constraints: const BoxConstraints(minWidth: 600),
        height: useColumnLayout ? null : 225,
        width: double.infinity,
        margin: MediaQuery.of(context).size.width <= 992
            ? const EdgeInsets.symmetric(horizontal: 25, vertical: 4)
            : const EdgeInsets.symmetric(horizontal: 225, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: commonDecoration,
        child: useColumnLayout
            ? SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFilters(viewModel, 16, 1.0),
                    const SizedBox(height: 12),
                    _buildTotalCounts(viewModel, 16, 1.0),
                    const SizedBox(height: 12),
                    _buildStatusCounts(viewModel, 16, 1.0),
                    const SizedBox(height: 12),
                    _buildAlerts(viewModel, 12, 1.0),
                  ],
                ),
              )
            : Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: _buildFilters(viewModel, desktopFontSize * 0.85,
                        scaleFactorAdjust * 0.85),
                  ),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  const VerticalDivider(color: Colors.grey, width: 1),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  Expanded(
                    child: _buildTotalCounts(viewModel, desktopFontSize * 0.85,
                        scaleFactorAdjust * 0.85),
                  ),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  const VerticalDivider(color: Colors.grey, width: 1),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  Expanded(
                    child: _buildStatusCounts(viewModel, desktopFontSize * 0.85,
                        scaleFactorAdjust * 0.85),
                  ),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  const VerticalDivider(color: Colors.grey, width: 1),
                  SizedBox(width: 8 * scaleFactorAdjust * 0.85),
                  Expanded(
                    child:
                        _buildAlerts(viewModel, 12, scaleFactorAdjust * 0.85),
                  ),
                ],
              ),
      ),
      const SizedBox(height: 15),
      // Table Container
      Container(
        width: double.infinity,
        margin: MediaQuery.of(context).size.width <= 992
            ? const EdgeInsets.symmetric(horizontal: 25, vertical: 4)
            : const EdgeInsets.symmetric(horizontal: 225, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: commonDecoration,
        child: needsHorizontalScroll
            ? SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: _buildCustomTable(
                    context, viewModel, headerFontSize, cellFontSize),
              )
            : _buildCustomTable(
                context, viewModel, headerFontSize, cellFontSize),
      ),
    ],
  );
}

Widget _buildCustomTable(
  BuildContext context,
  _ViewModel viewModel,
  double headerFontSize,
  double cellFontSize,
) {
  return CustomTable(
        columns: const [
          DataColumn(
            label: Text(
              'Live',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
            ),
          ),
          DataColumn(
            label: Text(
              'Name',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
            ),
          ),
          DataColumn(
            label: Text(
              'Location',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
            ),
          ),
          DataColumn(
            label: Text(
              'Punch In',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
            ),
          ),
          // DataColumn(
          //   label: Text(
          //     'Duration',
          //     style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
          //   ),
          // ),
          DataColumn(
            label: Text(
              'Alerts',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13 * 1.2),
            ),
          ),
        ],
        rows: _buildTableRows(context, viewModel, 1.0),
        setMobileAt: 0,
        allowMobile: false,
        headerHeight: 40.0,
        rowHeight: 20.0,
        stickyHeader: true,
        cellFontSize: 8.0,
        headerFontSize: 14.0,
        pagination: {
          "active": true,
          "rows": 8,
          "page": viewModel.firstTablePageIndex,
        },
        columnWidths: const {
          0: 0.03,
          1: 0.08,
          2: 0.12,
           3: 0.10,
          // 4: 0.08,
          5: 0.08,
    
        },
        onRowClick: (DataRow row) async {
          final rowIndex = (row.key as ValueKey<int>).value;
          secondaryMenu = true;
          viewModel.selectedMainTableRowIndex = rowIndex;
          viewModel.notifyListeners();
          viewModel.employeeSelected = true;
          final username = (row.cells[1].child as Text).data ?? '';
          final punchInTime = (row.cells[3].child as Text).data ?? '';

          if (username.isNotEmpty && punchInTime.isNotEmpty) {
            // Find the user by username
            final userEntry = viewModel.userMap.entries.firstWhere(
              (entry) => entry.value.username == username,
              orElse: () => MapEntry(
                '',
                User(
                  createdOn: DateTime.now(),
                  id: '',
                  username: '',
                  firstName: '',
                  lastName: '',
                  userTypeId: '',
                  isContact: false,
                  payRateFrequency: 0,
                  languageKey: '',
                ),
              ),
            );

            // If found, load that employee's punch cards
            if (userEntry.key.isNotEmpty) {
              final userId = userEntry.key;
              viewModel.selectedEmployeeUserId = userId;
              viewModel.updateSelectedEmployeePunchCards(userId);
              viewModel.notifyListeners();
              final punchCard = viewModel.filteredPunchCards[rowIndex];
              // ─────────────────────────────────────────────────────────
              // Call your handleRowTap (which does the camera offset)
              // ─────────────────────────────────────────────────────────
              await viewModel.handleRowTap(punchCard, rowIndex);
            }
          }
        },
      );
}

// Common decoration
final BoxDecoration commonTableDecoration = BoxDecoration(
  color: Colors.white,
  border: Border.all(
    color: ColorHelper.thePunchDesktopLightGray(),
    width: 1,
  ),
  borderRadius: BorderRadius.circular(20),
  boxShadow: [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 12,
      offset: const Offset(0, 6),
    ),
  ],
);

Widget _buildSecondExpandedWidget(
    BuildContext context, _ViewModel viewModel, bool isMobile) {
  final double headerFontSize = 12.0;
  final double cellFontSize = 9.0;

  final double screenWidth = MediaQuery.of(context).size.width;
  final bool needsHorizontalScroll =
      screenWidth <= 800 || (screenWidth >= 1024 && screenWidth <= 1400);

  return Expanded(
    //height: 305,
    // margin: const EdgeInsets.all(10),
    // decoration: BoxDecoration(
    //   border: Border.all(
    //     color: ColorHelper.thePunchAdminButtonBlue()
    //         .withOpacity(.2), // Border color
    //     width: 1.0, // Border width
    //   ),
    //   borderRadius: BorderRadius.circular(20), // Rounded corners
    // ),
    child: SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child:  Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Employee Overview Container
        Container(
          padding: const EdgeInsets.all(30),
          decoration: BoxDecoration(
            color: ColorHelper.thePunchAdminButtonBlue().withOpacity(.1),
            borderRadius: BorderRadius.circular(20),
            // gradient: LinearGradient(
            //   colors: [
            //     ColorHelper.thePunchAdminButtonBlue().withOpacity(.2),
            //     ColorHelper.thePunchAdminButtonBlue().withOpacity(0.2),
            //   ],
            //   begin: Alignment.topLeft,
            //   end: Alignment.bottomRight,
            // ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Title and Button Row
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      viewModel.selectedEmployeeFullName,
                      style: TextStyle(
                        fontSize: isMobile ? 16.0 : 24.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  if(isMobile)
                  _buildDetailRow('Total Hours:', viewModel.employeeTotal,
                      isMobile ? 14.0 : 12.0),
                  if(!isMobile)
                  const SizedBox(width: 20),
                  const SizedBox(width: 20),
                  Container(
                    decoration: BoxDecoration(
                      color: const ui.Color.fromARGB(255, 1, 52, 94),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close, color: Colors.white, size:14),
                      tooltip: 'Close Employee Details',
                      onPressed: () {
                        viewModel.employeeSelected = false;
                        viewModel.notifyListeners();
                      },
                    ),
                  )
                ],
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if(!isMobile)
                  _buildDetailRow('Total Hours:', viewModel.employeeTotal,
                      isMobile ? 14.0 : 12.0),
                  const SizedBox(width: 20),
                  _buildDetailRow(
                      'Total Punch Cards:',
                      '${viewModel.selectedEmployeePunchCards.length}',
                      isMobile ? 14.0 : 12.0),
                  const SizedBox(width: 20),
                  ElevatedButton(
                    onPressed: () async {
                      final employeeId = viewModel.selectedEmployeeUserId;
                      print(employeeId);
                      if (employeeId != null && employeeId.isNotEmpty) {
                        await context.pushNamed(
                          '/employees/view',
                          queryParameters: {
                            'id': employeeId,
                          },
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('No employee selected.'),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'View Full Profile',
                      style: TextStyle(
                        fontSize: isMobile ? 12.0 : 14.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        _buildSecondCustomTable(
            context, viewModel, headerFontSize, cellFontSize),

        // Second Table Container
      ],
    ),
  ));
}

Widget _buildDetailRow(String label, String value, double fontSize) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        label,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
      ),
      Text(
        value,
        style: TextStyle(
          fontSize: fontSize * 1.5,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    ],
  );
}

Widget _buildSecondCustomTable(BuildContext context, _ViewModel viewModel,
    double headerFontSize, double cellFontSize) {
  return
      //child:
      ConstrainedBox(
    constraints: const BoxConstraints(
      maxWidth: double.infinity,
      maxHeight: 400,
    ),
    child: SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: CustomTable(
          columns: const [
            DataColumn(
              label: Text('Location',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  )),
            ),
            DataColumn(
              label: Text('Punch In',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  )),
            ),
            DataColumn(
              label: Text('Punch Out',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  )),
            ),
            DataColumn(
              label: Text('Duration',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  )),
            ),
            DataColumn(
              label: Text('Alerts',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  )),
            ),
          ],
          rows: _buildSecondTableRows(context, viewModel, 1.0),
          setMobileAt: 625,
          allowMobile: false,
          headerHeight: 40.0,
          rowHeight: 20.0,
          stickyHeader: true,
          cellFontSize: 18.0,
          headerFontSize: 14.0,
          pagination: {
            "active": true,
            "rows": 8,
            "page": viewModel.secondTablePageIndex, // <-- new line
          },
          columnWidths: const {
            0: 0.15,
            1: .15,
            2: .16,
            3: .16,
            4: .16,
          },
          onRowClick: (DataRow row) async {
            viewModel.employeeSelected = true;
            final punchCardId = (row.key as ValueKey).value.toString();
            // Find the corresponding punch card in selectedEmployeePunchCards list
            final punchCard = viewModel.selectedEmployeePunchCards
                .firstWhere((card) => card.id == punchCardId);
            final rowIndex =
                viewModel.selectedEmployeePunchCards.indexOf(punchCard);
            viewModel.selectedEmployeeTableRowIndex = rowIndex;
            viewModel.notifyListeners();

            // Update the map marker and UI by invoking handleRowTap, which animates the map
            await viewModel.handleRowTap(punchCard, rowIndex);
          },
        ),
      ),
    ),
  );
}

List<DataRow> _buildSecondTableRows(
    BuildContext context, _ViewModel viewModel, double scaleFactor) {
  final locale = Localizations.localeOf(context);

  return viewModel.selectedEmployeePunchCards.map((punchCard) {
    final location = viewModel.locationMap[punchCard.locationId];

    // 1) Convert to local
    final clockedInLocal = punchCard.clockedIn.toLocal();
    final clockedOutLocal = punchCard.clockedOut?.toLocal();

    final alerts = viewModel.alerts
        .where((alert) =>
            alert.punchCardId == punchCard.id &&
            alert.alertTypeId != Alert.reconnectedGeofenceId)
        .toList();

    return DataRow(
      key: ValueKey(punchCard.id),
      cells: [
        // Column 0 => Location
        DataCell(
          Text(
            location?.name ?? 'Unknown',
            style: TextStyle(fontSize: 14 * scaleFactor),
          ),
        ),
        // Column 1 => Punch In (UTC -> Local)
        DataCell(
          Text(
            DateFormat('hh:mm a', locale.languageCode).format(clockedInLocal),
            style: TextStyle(fontSize: 14 * scaleFactor),
          ),
        ),
        // Column 2 => Punch Out (UTC -> Local or "---")
        DataCell(
          Text(
            clockedOutLocal != null
                ? DateFormat('hh:mm a', locale.languageCode)
                    .format(clockedOutLocal)
                : '---',
            style: TextStyle(fontSize: 14 * scaleFactor),
          ),
        ),
        // Column 3 => Duration
        DataCell(
          Text(
            punchCard.duration?.toFormatted ?? '---',
            style: TextStyle(fontSize: 14 * scaleFactor),
          ),
        ),
        // Column 4 => Alerts
        DataCell(
          alerts.isEmpty
              ? Text('None', style: TextStyle(fontSize: 14 * scaleFactor))
              : Wrap(
                  spacing: 4 * scaleFactor,
                  runSpacing: 4 * scaleFactor,
                  children: alerts
                      .map((alert) => alert.alertTypeId)
                      .toSet()
                      .map((typeId) {
                    final color =
                        viewModel.alertColors[typeId] ?? Colors.transparent;
                    return Container(
                      width: 16 * scaleFactor,
                      height: 16 * scaleFactor,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    );
                  }).toList(),
                ),
        ),
      ],
    );
  }).toList();
}

Widget _buildInfoBadge(String label, String value, double fontSize,
        bool isLargeScreen, double scaleFactor) =>
    Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: fontSize * scaleFactor,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        SizedBox(width: 6 * scaleFactor),
        Container(
          padding: EdgeInsets.symmetric(
              horizontal: 12 * scaleFactor, vertical: 6 * scaleFactor),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20 * scaleFactor),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: fontSize * scaleFactor,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );

Widget _buildFilters(
        _ViewModel viewModel, double fontSize, double scaleFactor) =>
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filters',
          style: TextStyle(
            fontSize: fontSize * scaleFactor,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchRed(),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        SizedBox(height: 8 * scaleFactor),
        Wrap(
          spacing: 8 * scaleFactor,
          runSpacing: 8 * scaleFactor,
          children: [
            _FilterButton(
              label: 'Today',
              filterKey: 'today',
              textColor: ColorHelper.thePunchRed(),
              scaleFactor: scaleFactor,
              onSelected: viewModel.applyFilter,
              isSelected: viewModel.selectedFilter == 'today',
            ),
            _FilterButton(
              label: 'Week',
              filterKey: 'week',
              textColor: ColorHelper.thePunchRed(),
              scaleFactor: scaleFactor,
              onSelected: viewModel.applyFilter,
              isSelected: viewModel.selectedFilter == 'week',
            ),
            _FilterButton(
              label: 'Month',
              filterKey: 'month',
              textColor: ColorHelper.thePunchRed(),
              scaleFactor: scaleFactor,
              onSelected: viewModel.applyFilter,
              isSelected: viewModel.selectedFilter == 'month',
            ),
            _FilterButton(
              label: 'Live',
              filterKey: 'live',
              textColor: const Color.fromARGB(255, 0, 168, 87),
              scaleFactor: scaleFactor,
              onSelected: viewModel.applyFilter,
              isSelected: viewModel.selectedFilter == 'live',
            ),
            _FilterButton(
              label: 'Scheduled',
              filterKey: 'scheduled',
              textColor: Colors.blueGrey.shade400,
              scaleFactor: scaleFactor,
              onSelected: viewModel.applyFilter,
              isSelected: viewModel.selectedFilter == 'scheduled',
            ),
          ],
        ),
      ],
    );

/// HERE IS WHERE WE ADD THE YEAR DROPDOWN (look for the comments: "ADDED FOR YEAR DROPDOWN")
Widget _buildTotalCounts(
        _ViewModel viewModel, double fontSize, double scaleFactor) =>
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Total Counts',
          style: TextStyle(
            fontSize: fontSize * scaleFactor,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchRed(),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        SizedBox(height: 8 * scaleFactor),

        // ─────────────────────────────────────────────────────────────────────
        // Row containing BOTH the Month and Year Dropdowns
        // ─────────────────────────────────────────────────────────────────────
        Row(
          children: [
            // ─────────────────────────────────────────────────────────
            // MONTH DROPDOWN
            // ─────────────────────────────────────────────────────────
            DropdownButton<String>(
              padding: const EdgeInsets.all(5),

              value: viewModel.selectedMonth,
              icon: const Icon(Icons.arrow_drop_down,
                  color: Color.fromARGB(255, 43, 43, 43)),
              // 1) Black text for the selected value & the hint.
              style: const TextStyle(
                  color: Color.fromARGB(255, 43, 43, 43),
                  fontWeight: FontWeight.w600),
              hint: const Text(
                "Month",
                style: TextStyle(color: Color.fromARGB(255, 43, 43, 43)),
              ),

              // 2) Dark background on the dropdown menu
              dropdownColor: Colors.blueGrey.shade700,

              // 3) The dropdown arrow is white
              iconEnabledColor: Colors.white,

              // 4) White text for the menu items
              items: viewModel.months.map((month) {
                return DropdownMenuItem<String>(
                  value: month,
                  child: Text(
                    month,
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              }).toList(),

              // 5) Force the selected item text to be black
              selectedItemBuilder: (BuildContext context) {
                return viewModel.months.map((month) {
                  return Text(
                    month,
                    style: const TextStyle(
                        color: Color.fromARGB(255, 43, 43, 43),
                        fontWeight: FontWeight.w600),
                  );
                }).toList();
              },

              // 6) Change handler
              onChanged: (selectedMonth) async {
                if (selectedMonth != null) {
                  await viewModel.applyMonthFilter(selectedMonth);
                }
              },
              isDense: true,
            ),

            const SizedBox(width: 5),

            // ─────────────────────────────────────────────────────────
            // YEAR DROPDOWN
            // ─────────────────────────────────────────────────────────
            SizedBox(
              width: 66, // Adjust to your liking
              child: DropdownButton<int>(
                icon: const Icon(Icons.arrow_drop_down,
                    color: Color.fromARGB(255, 43, 43, 43)),
                value: viewModel.selectedYear,

                // 1) Black text for selected value & hint
                style: const TextStyle(
                    color: Color.fromARGB(255, 43, 43, 43),
                    fontWeight: FontWeight.w600),
                hint: const Text(
                  "Year",
                  style: TextStyle(color: Colors.black),
                ),

                // 2) Dark background on the dropdown
                dropdownColor: Colors.blueGrey.shade700,

                // 3) White icon
                iconEnabledColor: Colors.white,

                // 4) White text for menu items
                items: viewModel.years.map((year) {
                  return DropdownMenuItem<int>(
                    value: year,
                    child: Text(
                      year.toString(),
                      style: const TextStyle(color: Colors.white),
                    ),
                  );
                }).toList(),

                // 5) Force selected item text to be black
                selectedItemBuilder: (BuildContext context) {
                  return viewModel.years.map((year) {
                    return Text(
                      year.toString(),
                      style: const TextStyle(
                          color: Color.fromARGB(255, 43, 43, 43),
                          fontWeight: FontWeight.w600),
                    );
                  }).toList();
                },

                // 6) Change handler
                onChanged: (selectedYear) async {
                  if (selectedYear != null) {
                    viewModel.selectedYear = selectedYear;
                    // Re-apply the filter or handle however you like
                    final currentFilter = viewModel.selectedFilter;
                    if (currentFilter.isEmpty || currentFilter == 'all') {
                      await viewModel.applyFilter('month');
                    } else {
                      await viewModel.applyFilter(currentFilter);
                    }
                  }
                },
                isDense: true,
              ),
            ),
          ],
        ),
        // ─────────────────────────────────────────────────────────────────────
        // End of Row for Month + Year
        // ─────────────────────────────────────────────────────────────────────

        SizedBox(height: 8 * scaleFactor),
        Row(
          children: [
            Text(
              'Total:',
              style: TextStyle(
                  fontSize: 14 * scaleFactor, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            SizedBox(width: 4 * scaleFactor),
            Flexible(
              child: Text(
                viewModel.totalCount.toString(),
                style: TextStyle(
                    fontSize: 20 * scaleFactor, fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
        SizedBox(height: 4 * scaleFactor),
        Row(
          children: [
            Text(
              'Hours:',
              style: TextStyle(
                  fontSize: 14 * scaleFactor, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            SizedBox(width: 4 * scaleFactor),
            Flexible(
              child: Text(
                viewModel.formattedTotalHours,
                style: TextStyle(
                    fontSize: 20 * scaleFactor, fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ],
    );

Widget _buildStatusCounts(
        _ViewModel viewModel, double fontSize, double scaleFactor) =>
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status Counts',
          style: TextStyle(
            fontSize: fontSize * scaleFactor,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchRed(),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        SizedBox(height: 8 * scaleFactor),
        _InfoRow(
            label: 'Live:',
            value: viewModel.liveCount.toString(),
            scaleFactor: scaleFactor),
        _InfoRow(
            label: 'Scheduled:',
            value: viewModel.scheduledCount.toString(),
            scaleFactor: scaleFactor),
        _InfoRow(
            label: 'Completed:',
            value: viewModel.completedCount.toString(),
            scaleFactor: scaleFactor),
      ],
    );

Widget _buildAlerts(
        _ViewModel viewModel, double fontSize, double scaleFactor) =>
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Alerts',
          style: TextStyle(
            fontSize: (fontSize * scaleFactor) + 2,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchRed(),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        SizedBox(height: 8 * scaleFactor),
        _AlertItem(
          title: 'Early/Late Punch-In:',
          filterKey: 'earlyLatePunchIn',
          count: viewModel.earlyLatePunchInCount,
          color: const Color(0xFFFFD500),
          fontSize: 14,
          scaleFactor: scaleFactor,
          onSelected: viewModel.applyFilter,
          isSelected: viewModel.selectedFilter == 'earlyLatePunchIn',
        ),
        _AlertItem(
          title: 'Early/Late Punch-Out:',
          filterKey: 'earlyLatePunchOut',
          count: viewModel.earlyLatePunchOutCount,
          color: const Color(0xFFFF6F00),
          fontSize: 14,
          scaleFactor: scaleFactor,
          onSelected: viewModel.applyFilter,
          isSelected: viewModel.selectedFilter == 'earlyLatePunchOut',
        ),
        _AlertItem(
          title: 'Geofence Breached:',
          filterKey: 'geofenceBreach',
          count: viewModel.geofenceBreachCount,
          color: const Color(0xFFFF0000),
          fontSize: 14,
          scaleFactor: scaleFactor,
          onSelected: viewModel.applyFilter,
          isSelected: viewModel.selectedFilter == 'geofenceBreach',
        ),
        _AlertItem(
          title: 'No Show:',
          filterKey: 'noShow',
          count: viewModel.noShowCount,
          color: const Color(0xFFAD2D0D),
          fontSize: 14,
          scaleFactor: scaleFactor,
          onSelected: viewModel.applyFilter,
          isSelected: viewModel.selectedFilter == 'noShow',
        ),
      ],
    );

// Build table rows
List<DataRow> _buildTableRows(
  BuildContext context,
  _ViewModel viewModel,
  double scaleFactor,
) {
  final locale = Localizations.localeOf(context);

  return viewModel.filteredPunchCards.asMap().entries.map((entry) {
    final index = entry.key;
    final punchCard = entry.value;
    final bool isSelected = (index == viewModel.selectedMainTableRowIndex);
    final Color rowColor = isSelected
        ? ColorHelper.thePunchRed().withOpacity(0.3)
        : (index % 2 == 0 ? Colors.grey.shade100 : Colors.white);

    final user = viewModel.userMap[punchCard.userId];
    final location = viewModel.locationMap[punchCard.locationId];
    final isLive = (punchCard.clockedOut == null);
    final isScheduled = (punchCard.id == 'scheduled');
    final textStyle = TextStyle(
      fontSize: 14 * scaleFactor,
      height: 1.0,
      fontWeight: FontWeight.w500,
    );

    // 1) Convert these to local before formatting
    final clockedInLocal = punchCard.clockedIn.toLocal();
    final clockedOutLocal = punchCard.clockedOut?.toLocal();

    // Decide text color
    Color pickTextColor() {
      if (isSelected) {
        return Colors.red;
      } else if (isScheduled) {
        return Colors.grey.shade400;
      } else {
        return Colors.black;
      }
    }

    return DataRow(
      key: ValueKey(index),
      selected: isSelected,
      color: MaterialStateProperty.resolveWith<Color?>((states) => rowColor),
      cells: [
        // Column 0 => Live indicator
        DataCell(
          Icon(
            isLive ? Icons.where_to_vote : Icons.where_to_vote_outlined,
            color: isLive ? ColorHelper.thePunchAccentRed() : Colors.grey[200],
            size: 25.0,
          ),
        ),
        // Column 1 => Username
        DataCell(
          Text(
            user?.username ?? 'Unknown',
            style: textStyle.copyWith(color: Colors.black),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        // Column 2 => Location
        DataCell(
          Text(
            location?.name ?? '---',
            style: textStyle.copyWith(color: Colors.black),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        // Column 3 => Punch In (UTC -> Local)
        DataCell(
          Text(
            // 2) Format the *local* DateTime
            DateFormat('hh:mm a', locale.languageCode).format(
              clockedInLocal,
            ),
            style: textStyle.copyWith(color: Colors.black),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        // Column 4 => Punch Out (UTC -> Local or "---")
        // DataCell(
        //   Text(
        //     clockedOutLocal != null
        //         ? DateFormat('hh:mm a', locale.languageCode).format(
        //             clockedOutLocal,
        //           )
        //         : '---',
        //     style: textStyle.copyWith(color: pickTextColor()),
        //     overflow: TextOverflow.ellipsis,
        //     maxLines: 1,
        //   ),
        // ),
        // Column 5 => Duration
        // DataCell(
        //   Text(
        //     isScheduled || punchCard.duration == null
        //         ? '---'
        //         : punchCard.duration!.toFormatted, // hours/min string
        //     style: textStyle.copyWith(color: Colors.black),
        //     overflow: TextOverflow.ellipsis,
        //     maxLines: 1,
        //   ),
        // ),
        
        // Column 6 => Alerts
        DataCell(
          viewModel.alerts.isEmpty
              ? Text(
                  'None',
                  style: textStyle.copyWith(color: Colors.black),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                )
              : Wrap(
                  spacing: 4 * scaleFactor,
                  runSpacing: 4 * scaleFactor,
                  children: viewModel.alerts
                      .where((alert) =>
                          alert.punchCardId == punchCard.id &&
                          alert.alertTypeId != Alert.reconnectedGeofenceId)
                      .map((alert) => alert.alertTypeId)
                      .toSet()
                      .map((alertTypeId) {
                    final color = viewModel.alertColors[alertTypeId] ??
                        Colors.transparent;
                    return Container(
                      width: 14.0,
                      height: 14.0,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    );
                  }).toList(),
                ),
        ),
      ],
    );
  }).toList();
}
}
class SubtleScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) {
    return child;
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics();
  }
}

class PunchCardCountBox extends StatelessWidget {
  final String title;
  final String count;

  const PunchCardCountBox(this.title, this.count, {super.key});

  @override
  Widget build(BuildContext context) => Container(
        width: 125,
        height: 75,
        margin: const EdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: ColorHelper.thePunchDesktopLightGray()),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w700,
              ),
            ),
            Text(
              count,
              style: const TextStyle(
                fontSize: 35,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      );
}

class PunchCardCountBoxRed extends StatelessWidget {
  final String title;
  final String count;

  const PunchCardCountBoxRed(this.title, this.count, {super.key});

  @override
  Widget build(BuildContext context) => Container(
        width: 125,
        height: 75,
        margin: const EdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: ColorHelper.thePunchRed(),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
            Text(
              count,
              style: const TextStyle(
                fontSize: 35,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
          ],
        ),
      );
}

Widget _InfoRow(
        {required String label,
        required String value,
        required double scaleFactor}) =>
    Padding(
      padding: EdgeInsets.symmetric(vertical: 2.0 * scaleFactor),
      child: Row(
        children: [
          Text(
            '$label ',
            style: TextStyle(
              fontSize: 14 * scaleFactor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14 * scaleFactor,
            ),
          ),
        ],
      ),
    );

class _AlertItem extends StatefulWidget {
  final String title;
  final String filterKey;
  final int count;
  final Color color;
  final double fontSize;
  final double scaleFactor;
  final bool isSelected;
  final Function(String) onSelected;

  const _AlertItem({
    Key? key,
    required this.title,
    required this.filterKey,
    required this.count,
    required this.color,
    required this.fontSize,
    required this.scaleFactor,
    required this.isSelected,
    required this.onSelected,
  }) : super(key: key);

  @override
  State<_AlertItem> createState() => _AlertItemState();
}

class _AlertItemState extends State<_AlertItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MouseRegion(
        onEnter: (_) => _controller.forward(),
        onExit: (_) => _controller.reverse(),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTap: () => widget.onSelected(widget.filterKey),
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 4 * widget.scaleFactor),
              padding: EdgeInsets.symmetric(
                  horizontal: 16 * widget.scaleFactor,
                  vertical: 12 * widget.scaleFactor),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? widget.color.withOpacity(0.7)
                    : widget.color.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12 * widget.scaleFactor),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8 * widget.scaleFactor,
                    offset: Offset(0, 4 * widget.scaleFactor),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.white,
                    size: 15 * widget.scaleFactor,
                  ),
                  SizedBox(width: 8 * widget.scaleFactor),
                  Text(
                    '${widget.title} ${widget.count}',
                    style: TextStyle(
                      fontSize: (widget.fontSize * widget.scaleFactor) - 2,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}

class _FilterButton extends StatefulWidget {
  final String label;
  final String filterKey;
  final Color textColor;
  final bool isSelected;
  final Function(String) onSelected;
  final double scaleFactor;

  const _FilterButton({
    Key? key,
    required this.label,
    required this.filterKey,
    required this.textColor,
    required this.isSelected,
    required this.onSelected,
    required this.scaleFactor,
  }) : super(key: key);

  @override
  State<_FilterButton> createState() => _FilterButtonState();
}

class _FilterButtonState extends State<_FilterButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MouseRegion(
        onEnter: (_) => _controller.forward(),
        onExit: (_) => _controller.reverse(),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: GestureDetector(
            onTap: () => widget.onSelected(widget.filterKey),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16 * widget.scaleFactor,
                vertical: 8 * widget.scaleFactor,
              ),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? widget.textColor.withOpacity(0.2)
                    : Colors.transparent,
                border: Border.all(color: widget.textColor),
                borderRadius: BorderRadius.circular(20 * widget.scaleFactor),
              ),
              child: Text(
                widget.label,
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: 12 * widget.scaleFactor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );
}

// ViewModel
class _ViewModel extends ChangeNotifier with ViewModelMixin {
  List<PunchCard> punchCards = [];
  List<Alert> alerts = [];
  List<Schedule> schedules = [];
  Map<String, Location> locationMap = {};
  Map<String, User> userMap = {};

  List<PunchCard> filteredPunchCards = [];
  List<PunchCard> selectedEmployeePunchCards = [];
  List<Alert> filteredAlerts = [];

  String? selectedEmployeeUserId;
  LatLng? selectedMarkerPosition;
  int? selectedMainTableRowIndex;
  int? selectedEmployeeTableRowIndex;
  PunchCard? selectedPunchCard;

  String formattedTotalHours = "0h 0m";

  int todayCount = 0;
  int yesterdayCount = 0;
  int thisWeekCount = 0;
  int lastWeekCount = 0;
  int thisMonthCount = 0;
  int lastMonthCount = 0;
  int? allCount;
  int totalCount = 0;
  int liveCount = 0;
  int scheduledCount = 0;
  int completedCount = 0;
  int earlyLatePunchInCount = 0;
  int earlyLatePunchOutCount = 0;
  int geofenceBreachCount = 0;
  int noShowCount = 0;
  bool employeeSelected = false;
  Set<Marker> markers = {};
  Set<Circle> circles = {};
  BitmapDescriptor? customIcon;
  BitmapDescriptor? customCircle;
  BitmapDescriptor? redMarker;
  BitmapDescriptor? grayMarker;
  BitmapDescriptor? greenMarker;
  Completer<GoogleMapController> mapController = Completer();

  int? currentMonth;
  String employeeTotal = '0h 0m';
  int employeeDay = 0;
  int employeeWeek = 0;
  int employeeMonth = 0;
  bool isLoading = true;

  // Months list
  List<String> months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];
  String? selectedMonth;

  // ADDED FOR YEAR DROPDOWN:
  List<int> years = [];
  int selectedYear = DateTime.now().year;

  String selectedFilter = 'all';

  final Map<String, Color> alertColors = {
    Alert.earlyPunchInId: const Color(0xFFFFD500),
    Alert.latePunchInId: const Color(0xFFFFD500),
    Alert.earlyPunchOutId: const Color(0xFFFF6F00),
    Alert.latePunchOutId: const Color(0xFFFF6F00),
    Alert.reconnectedGeofenceId: const Color.fromARGB(255, 228, 5, 217),
    Alert.outsideGeofenceId: const Color(0xFFFF0000),
    Alert.activeBreach: const Color(0xFFFF0000),
    Alert.noPunchInId: const Color(0xFFAD2D0D),
  };

  int firstTablePageIndex = 0;
  int secondTablePageIndex = 0;

  _ViewModel() {
    addListenables([
      DataModel().punchCardModel,
      DataModel().alertModel,
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);

    selectedMonth = months[DateTime.now().month - 1];
  }

  Future<void> initialize(BuildContext context) async {
    isLoading = true;
    notifyListeners();

    // Build the years list (for example, 2020..currentYear+1)
    // ADDED FOR YEAR DROPDOWN
    final currentYear = DateTime.now().year;
    for (int y = 2020; y <= currentYear + 1; y++) {
      years.add(y);
    }

    await SyncModel().sync();

    await clearData();
    await loadCustomIcons();
    await _loadCustomMarker();
    await refresh();

    selectedMonth ??= months[DateTime.now().month - 1];

    await applyFilter('today');
    await calculatePunchCardCounts();

    isLoading = false;
    notifyListeners();
  }

  @override
  Future<void> dispose() async {
    await clearData();
    super.dispose();
  }

  @override
  Future<void> refresh() async {
    isLoading = true;
    notifyListeners();
    try {
      punchCards = (await DataModel().punchCardModel.all).toList();
      alerts = (await DataModel().alertModel.all).toList();
      schedules = (await DataModel().scheduleModel.all).toList();

      liveCount = 0;
      scheduledCount = 0;
      completedCount = 0;
      earlyLatePunchInCount = 0;
      earlyLatePunchOutCount = 0;
      geofenceBreachCount = 0;
      noShowCount = 0;

      for (final alert in alerts) {
        switch (alert.alertTypeId) {
          case Alert.earlyPunchInId:
          case Alert.latePunchInId:
            earlyLatePunchInCount++;
            break;
          case Alert.earlyPunchOutId:
          case Alert.latePunchOutId:
            earlyLatePunchOutCount++;
            break;
          case Alert.outsideGeofenceId:
          case Alert.activeBreach:
            geofenceBreachCount++;
            break;
          case Alert.noPunchInId:
            noShowCount++;
            break;
        }
      }

      final now = DateTime.now();
      final filteredSchedules = schedules.where((schedule) {
        final scheduleDate = schedule.startDateLocal;
        return scheduleDate.isAfter(now.subtract(const Duration(hours: 24))) &&
            scheduleDate.isBefore(now.add(const Duration(hours: 48)));
      }).toList();

      final userIds = {
        ...punchCards.map((e) => e.userId),
        ...filteredSchedules.map((e) => e.userId),
      };
      final locationIds = {
        ...punchCards.map((e) => e.locationId).whereType<String>(),
        ...filteredSchedules.map((e) => e.locationId).whereType<String>(),
      };

      userMap = {
        for (var user in await DataModel().userModel.getByIds(userIds))
          user.id: user
      };
      locationMap = {
        for (var location
            in await DataModel().locationModel.getByIds(locationIds))
          location.id: location
      };

      addScheduledPunchCards(filteredSchedules);

      for (var punchCard in punchCards) {
        if (punchCard.clockedOut == null && punchCard.id != 'scheduled') {
          liveCount += 1;
        } else if (punchCard.clockedOut != null &&
            punchCard.id != 'scheduled') {
          completedCount += 1;
        }
      }

      await applyFilter('today');
      await calculatePunchCardCounts();
    } catch (e) {
      print("Error loading punch cards: $e");
    } finally {
      isLoading = false;
      await initializeMarkers();
      notifyListeners();
    }
  }

  void addScheduledPunchCards(List<Schedule> filteredSchedules) {
    final existingScheduleIds = punchCards
        .where((punchCard) => punchCard.scheduleId != null)
        .map((punchCard) => punchCard.scheduleId)
        .toSet();

    for (final schedule in filteredSchedules) {
      if (!existingScheduleIds.contains(schedule.id)) {
        punchCards.insert(
          0,
          PunchCard(
            id: 'scheduled',
            userId: schedule.userId,
            locationId: schedule.locationId,
            clockedIn: schedule.startDateLocal,
            clockedOut: schedule.endDateLocal,
            createdOn: schedule.createdOn,
            jobTypeId: '95E597BF-075A-4048-9ADF-C95E34163C97',
            scheduleId: schedule.id,
          ),
        );
        scheduledCount += 1;
      }
    }
  }

  Future<void> clearData() async {
    punchCards.clear();
    alerts.clear();
    schedules.clear();
    locationMap.clear();
    userMap.clear();
    filteredPunchCards.clear();
    selectedEmployeePunchCards.clear();
    selectedEmployeeUserId = null;
    selectedMarkerPosition = null;

    todayCount = 0;
    yesterdayCount = 0;
    thisWeekCount = 0;
    lastWeekCount = 0;
    thisMonthCount = 0;
    lastMonthCount = 0;
    allCount = null;
    totalCount = 0;
    liveCount = 0;
    scheduledCount = 0;
    completedCount = 0;
    earlyLatePunchInCount = 0;
    earlyLatePunchOutCount = 0;
    geofenceBreachCount = 0;
    noShowCount = 0;

    selectedMainTableRowIndex = null;
    selectedEmployeeTableRowIndex = null;
    selectedPunchCard = null;
    customIcon = null;
    customCircle = null;
    markers.clear();
    employeeTotal = '0h 0m';
    employeeDay = 0;
    employeeWeek = 0;
    employeeMonth = 0;
    isLoading = true;
    selectedFilter = 'all';
    selectedMonth = null;
    // Keep the 'years' list and 'selectedYear' intact, or re-generate later.

    notifyListeners();
  }

  Future<void> loadCustomIcons() async {
    try {
      redMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.where_to_vote_outlined,
        ColorHelper.thePunchRed(),
        45,
      );

      grayMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.watch_later,
        Colors.grey,
        45,
      );

      greenMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.person_pin_circle,
        Colors.green,
        45,
      );

      print('Custom markers loaded successfully.');
    } catch (e) {
      print('Error loading custom markers: $e');
      redMarker =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      grayMarker =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      greenMarker =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  Future<void> initializeMarkers() async {
    markers.clear();

    double? minLat, minLng, maxLat, maxLng;

    for (final punchCard in filteredPunchCards) {
      LatLng? markerPosition;

      // Determine "scheduled", "live", or "completed"
      final bool isScheduled = (punchCard.id == 'scheduled');
      final bool isLive = (punchCard.clockedOut == null && !isScheduled);
      final bool isCompleted = (punchCard.clockedOut != null && !isScheduled);

      // A) LIVE punch card => locationId OR last TravelPing
      if (isLive) {
        // 1) locationId if present
        if (punchCard.locationId != null) {
          final loc = locationMap[punchCard.locationId];
          if (loc != null) {
            markerPosition = LatLng(loc.latitude, loc.longitude);
          }
        }
        // 2) If STILL null, fallback to MOST RECENT TravelPing
        if (markerPosition == null) {
          final travelPings = await fetchTravelPings(punchCard.id);
          travelPings.sort((a, b) => a.createdOn.compareTo(b.createdOn));
          if (travelPings.isNotEmpty) {
            final newest = travelPings.last;
            final lat = double.tryParse(newest.startLat);
            final lng = double.tryParse(newest.startLong);
            if (lat != null && lng != null) {
              markerPosition = LatLng(lat, lng);
            }
          }
        }
      }
      // B) NOT LIVE => SCHEDULED or COMPLETED => locationId OR first TravelPing
      else if (isScheduled || isCompleted) {
        // 1) locationId if present
        if (punchCard.locationId != null) {
          final loc = locationMap[punchCard.locationId];
          if (loc != null) {
            markerPosition = LatLng(loc.latitude, loc.longitude);
          }
        }
        // 2) Fallback to FIRST (oldest) TravelPing
        if (markerPosition == null) {
          final travelPings = await fetchTravelPings(punchCard.id);
          travelPings.sort((a, b) => a.createdOn.compareTo(b.createdOn));
          if (travelPings.isNotEmpty) {
            final oldest = travelPings.first;
            final lat = double.tryParse(oldest.startLat);
            final lng = double.tryParse(oldest.startLong);
            if (lat != null && lng != null) {
              markerPosition = LatLng(lat, lng);
            }
          }
        }
      }

      // Skip if no position found
      if (markerPosition == null) {
        continue;
      }

      // Choose the marker color
      BitmapDescriptor icon;
      String markerIdValue;
      double zIndexValue = 1.0;

      if (isScheduled) {
        icon = grayMarker ?? BitmapDescriptor.defaultMarker;
        markerIdValue = 'punchcard-${punchCard.scheduleId}';
      } else if (isLive) {
        icon = greenMarker ?? BitmapDescriptor.defaultMarker;
        markerIdValue = 'punchcard-${punchCard.id}';
        zIndexValue = 2.0; // keep live markers on top
      } else {
        // completed
        icon = redMarker ?? BitmapDescriptor.defaultMarker;
        markerIdValue = 'punchcard-${punchCard.id}';
      }

      // ─────────────────────────────────────────────────────────
      // UPDATED: Call handleMarkerTap(...) for the offset logic
      // ─────────────────────────────────────────────────────────
      markers.add(
        Marker(
          markerId: MarkerId(markerIdValue),
          position: markerPosition,
          icon: customIcon ?? icon,
          // Adjust the anchor to shift the marker upward so its center aligns with the circle's center.
          // You might need to fine-tune these values based on your marker image.
          //anchor: const Offset(0.5, 0.3),
          zIndex: zIndexValue,
          onTap: () async {
            final rowIndex = filteredPunchCards.indexOf(punchCard);
            await handleMarkerTap(punchCard, rowIndex);
          },
        ),
      );
      //  markers.add(
      //   Marker(
      //     markerId: MarkerId(markerIdValue+'circle'),
      //     position: markerPosition,
      //     icon: customCircle ?? icon,
      //     // Adjust the anchor to shift the marker upward so its center aligns with the circle's center.
      //     // You might need to fine-tune these values based on your marker image.
      //     //anchor: const Offset(1, 1),
      //     zIndex: zIndexValue,
      //     onTap: () async {
      //   final rowIndex = filteredPunchCards.indexOf(punchCard);
      //   await handleMarkerTap(punchCard, rowIndex);
      //     },
      //   ),

      // );
      circles.add(
        Circle(
          circleId: CircleId(markerIdValue),
          center:
              markerPosition, // The circle remains centered on the marker position.
          radius: 750, // Radius in meters
          fillColor: Colors.red.withOpacity(0.05),
          strokeWidth: 0,
        ),
      );
      // Expand bounding box
      minLat = (minLat == null || markerPosition.latitude < minLat)
          ? markerPosition.latitude
          : minLat;
      maxLat = (maxLat == null || markerPosition.latitude > maxLat)
          ? markerPosition.latitude
          : maxLat;
      minLng = (minLng == null || markerPosition.longitude < minLng)
          ? markerPosition.longitude
          : minLng;
      maxLng = (maxLng == null || markerPosition.longitude > maxLng)
          ? markerPosition.longitude
          : maxLng;
    }

    // Auto-fit bounds if possible
    if (minLat != null && maxLat != null && minLng != null && maxLng != null) {
      final bounds = LatLngBounds(
        southwest: LatLng(minLat, minLng),
        northeast: LatLng(maxLat, maxLng),
      );
      try {
        final controller = await mapController.future;
        await controller
            .animateCamera(CameraUpdate.newLatLngBounds(bounds, 12));
      } catch (e) {
        print("Auto-fit error => $e");
      }
    } else {
      // fallback to a default location
      try {
        final controller = await mapController.future;
        await controller.animateCamera(
          CameraUpdate.newLatLngZoom(const LatLng(37.7749, -122.4194), 12),
        );
      } catch (e) {
        print("Error setting default camera => $e");
      }
    }

    notifyListeners();
  }

  Future<BitmapDescriptor> getResizedMarkerIcon(String assetPath,
      {int width = 70, int height = 70}) async {
    final ByteData data = await rootBundle.load(assetPath);
    final codec = await ui.instantiateImageCodec(
      data.buffer.asUint8List(),
      targetWidth: width,
      targetHeight: height,
    );
    final frame = await codec.getNextFrame();
    final ui.Image image = frame.image;

    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final resizedBytes = byteData!.buffer.asUint8List();

    return BitmapDescriptor.fromBytes(resizedBytes);
  }

  Future<void> _loadCustomMarker() async {
    try {
      final icon = await getResizedMarkerIcon('images/custom_location.png',
          width: 40, height: 40);
      final icon2 = await getResizedMarkerIcon('images/circle.png',
          width: 100, height: 100);

      customIcon = icon;
      customCircle = icon2;
    } catch (e) {
      print('Error loading custom marker: $e');
    }
  }

  /// Use the same offset & steps as handleRowTap
  Future<void> handleMarkerTap(PunchCard punchCard, int rowIndex) async {
    final markerIdValue = (punchCard.id == 'scheduled')
        ? 'punchcard-${punchCard.scheduleId}'
        : 'punchcard-${punchCard.id}';

    // 1) Find the corresponding marker
    Marker? foundMarker;
    try {
      foundMarker = markers.firstWhere(
        (m) => m.markerId.value == markerIdValue,
      );
    } catch (e) {
      print('Marker not found for ID: $markerIdValue');
      return;
    }

    // 3) Animate camera to that marker at zoom=19
    final GoogleMapController controller = await mapController.future;
    const double zoomLevel = 13.0;
    await controller.animateCamera(
      CameraUpdate.newLatLngZoom(foundMarker.position, zoomLevel),
    );

    // 2) Update punch card selection, highlight table row, etc.
    selectedPunchCard = punchCard;
    selectedMainTableRowIndex = rowIndex;
    updateSelectedEmployeePunchCards(punchCard.userId);

    // (Optional) Set an initial selectedMarkerPosition so the UI knows a marker was picked
    //selectedMarkerPosition = foundMarker.position;

    //notifyListeners();

    // 4) Optional short delay so the map "settles" at the new zoom
    //await Future.delayed(const Duration(milliseconds: 400));

    // 5) Get the screen coordinate of the marker (before offset)
    final ScreenCoordinate screenPoint =
        await controller.getScreenCoordinate(foundMarker.position);

    // 6) Compute an offset so the marker is lower on screen, e.g. y - 100
    final ScreenCoordinate offsetPoint = ScreenCoordinate(
      x: screenPoint.x,
      y: screenPoint.y - 100,
    );

    // 7) Convert that offset back to a LatLng
    final LatLng offsetLatLng = await controller.getLatLng(offsetPoint);

    // 8) Animate camera again to that "shifted" LatLng
    await controller.animateCamera(
      CameraUpdate.newLatLngZoom(offsetLatLng, zoomLevel),
    );

    // 9) Final small delay so the map fully settles after the second animate
    await Future.delayed(const Duration(milliseconds: 200));

    // 10) Re-assign `selectedMarkerPosition` so your FutureBuilder re-checks the final camera vantage

    selectedMarkerPosition = foundMarker.position;
    notifyListeners();
  }

  void restoreAllMarkers() async {
    markers.clear();
    await initializeMarkers();
    notifyListeners();
  }

  void onMapTap() {
    clearSelectedPunchCard();
  }

  Future<void> selectPunchCard(PunchCard punchCard, LatLng position) async {
    selectedPunchCard = punchCard;
    selectedMarkerPosition = position;
    notifyListeners();
  }

  void clearSelectedPunchCard() {
    selectedPunchCard = null;
    selectedMarkerPosition = null;
    notifyListeners();
  }

  Future<ScreenCoordinate?> getMarkerScreenPosition() async {
    if (selectedMarkerPosition == null) {
      print('Selected marker position is null.');
      return null;
    }

    try {
      final controller = await mapController.future;
      final screenCoordinate =
          await controller.getScreenCoordinate(selectedMarkerPosition!);
      print('Screen coordinate: $screenCoordinate');
      return screenCoordinate;
    } catch (e) {
      print('Error in getting marker screen position: $e');
      return null;
    }
  }

  Future<void> handleRowTap(PunchCard punchCard, int rowIndex) async {
    final markerIdValue = (punchCard.id == 'scheduled')
        ? 'punchcard-${punchCard.scheduleId}'
        : 'punchcard-${punchCard.id}';

    // Find the corresponding Marker
    Marker? foundMarker;
    try {
      foundMarker = markers.firstWhere(
        (marker) => marker.markerId.value == markerIdValue,
      );
    } catch (e) {
      print('Marker not found for ID: $markerIdValue');
      foundMarker = null;
    }

    if (foundMarker != null) {
      // 1) Update selectedPunchCard and highlight the row
      await selectPunchCard(punchCard, foundMarker.position);
      selectedMainTableRowIndex = rowIndex;
      updateSelectedEmployeePunchCards(punchCard.userId);
      notifyListeners();

      // 2) Animate camera to the marker (zoom level = 19)
      final GoogleMapController controller = await mapController.future;
      const double zoomLevel = 13.0;
      await controller.animateCamera(
        CameraUpdate.newLatLngZoom(foundMarker.position, zoomLevel),
      );

      // 3) OPTIONAL: If the map hasn't fully "settled" by the time
      //    we request the screen coordinate, we might need a tiny delay:
      // await Future.delayed(const Duration(milliseconds: 200));

      // 4) Compute the screen coordinate for that marker
      final ScreenCoordinate screenPoint =
          await controller.getScreenCoordinate(foundMarker.position);

      // 5) Offset that coordinate downward so the marker is lower on the screen
      //    Increase the offset as needed (e.g., 200, 300, 500, etc.)
      final ScreenCoordinate offsetPoint = ScreenCoordinate(
        x: screenPoint.x,
        y: screenPoint.y,
      );

      // 6) Convert the offset screen coordinate back to LatLng
      final LatLng offsetLatLng = await controller.getLatLng(offsetPoint);

      // 7) Animate the camera to this "shifted" LatLng at the same zoom level
      await controller.animateCamera(
        CameraUpdate.newLatLngZoom(offsetLatLng, zoomLevel),
      );
    }
  }

  Future<void> resetFirstTablePagination() async {
    firstTablePageIndex = 0;
    notifyListeners();
  }

  Future<void> resetSecondTablePagination() async {
    secondTablePageIndex = 0;
    notifyListeners();
  }

  void updateSelectedEmployeePunchCards(String userId) {
    if (selectedMonth == null) {
      selectedMonth = months[DateTime.now().month - 1];
    }

    final monthIndex = months.indexOf(selectedMonth!) + 1;

    // Use the selectedYear to filter as well
    selectedEmployeePunchCards = punchCards.where((card) {
      return card.userId == userId &&
          card.clockedIn.year == selectedYear &&
          card.clockedIn.month == monthIndex;
    }).toList();

    int totalMinutes = selectedEmployeePunchCards.fold<int>(
      0,
      (sum, card) => sum + (card.duration?.inMinutes ?? 0),
    );

    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    employeeTotal = '${hours}h ${minutes}m';

    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    employeeDay = selectedEmployeePunchCards.where((card) {
      return card.clockedIn.day == now.day &&
          card.clockedIn.month == now.month &&
          card.clockedIn.year == now.year;
    }).length;

    employeeWeek = selectedEmployeePunchCards.where((card) {
      return card.clockedIn
              .isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
          card.clockedIn.isBefore(startOfWeek.add(const Duration(days: 7)));
    }).length;

    employeeMonth = selectedEmployeePunchCards.length;
    notifyListeners();
  }

  Future<void> calculatePunchCardCounts() async {
    final now = DateTime.now();

    todayCount = punchCards
        .where((card) =>
            card.clockedIn.year == now.year &&
            card.clockedIn.month == now.month &&
            card.clockedIn.day == now.day)
        .length;

    final yesterday = now.subtract(const Duration(days: 1));
    yesterdayCount = punchCards
        .where((card) =>
            card.clockedIn.year == yesterday.year &&
            card.clockedIn.month == yesterday.month &&
            card.clockedIn.day == yesterday.day)
        .length;

    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    thisWeekCount = punchCards
        .where((card) =>
            card.clockedIn
                .isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
            card.clockedIn.isBefore(endOfWeek.add(const Duration(days: 1))))
        .length;

    final lastWeekStart = startOfWeek.subtract(const Duration(days: 7));
    final lastWeekEnd = startOfWeek.subtract(const Duration(days: 1));
    lastWeekCount = punchCards
        .where((card) =>
            card.clockedIn
                .isAfter(lastWeekStart.subtract(const Duration(days: 1))) &&
            card.clockedIn.isBefore(lastWeekEnd.add(const Duration(days: 1))))
        .length;

    thisMonthCount = punchCards
        .where((card) =>
            card.clockedIn.year == now.year &&
            card.clockedIn.month == now.month)
        .length;

    final lastMonth = DateTime(now.year, now.month - 1);
    lastMonthCount = punchCards
        .where((card) =>
            card.clockedIn.year == lastMonth.year &&
            card.clockedIn.month == lastMonth.month)
        .length;

    allCount = punchCards.length;
    notifyListeners();
  }

  void recalcAlertCounts(List<Alert> subsetOfAlerts) {
    earlyLatePunchInCount = 0;
    earlyLatePunchOutCount = 0;
    geofenceBreachCount = 0;
    noShowCount = 0;

    earlyLatePunchInCount = subsetOfAlerts.where((alert) {
      return alert.alertTypeId == Alert.earlyPunchInId ||
          alert.alertTypeId == Alert.latePunchInId;
    }).length;

    earlyLatePunchOutCount = subsetOfAlerts.where((alert) {
      return alert.alertTypeId == Alert.earlyPunchOutId ||
          alert.alertTypeId == Alert.latePunchOutId;
    }).length;

    geofenceBreachCount = subsetOfAlerts.where((alert) {
      return alert.alertTypeId == Alert.outsideGeofenceId ||
          alert.alertTypeId == Alert.activeBreach;
    }).length;

    noShowCount = subsetOfAlerts.where((alert) {
      return alert.alertTypeId == Alert.noPunchInId;
    }).length;
  }

  void recalcPunchCardCounts(List<PunchCard> subsetOfPunchCards) {
    totalCount = subsetOfPunchCards.length;

    liveCount = subsetOfPunchCards
        .where((pc) => pc.clockedOut == null && pc.id != 'scheduled')
        .length;

    scheduledCount =
        subsetOfPunchCards.where((pc) => pc.id == 'scheduled').length;

    completedCount = subsetOfPunchCards
        .where((pc) => pc.clockedOut != null && pc.id != 'scheduled')
        .length;

    final durations = subsetOfPunchCards
        .where((card) => card.id != 'scheduled' && card.duration != null)
        .map((card) => card.duration!)
        .toList();

    if (durations.isNotEmpty) {
      final totalMinutes =
          durations.fold<int>(0, (prev, d) => prev + d.inMinutes);
      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      formattedTotalHours = '${hours}h ${minutes}m';
    } else {
      formattedTotalHours = '0h 0m';
    }
  }

  // Updated applyFilter to also use 'selectedYear' if relevant
  Future<void> applyFilter(String filter) async {
    selectedFilter = filter;
    clearSelectedPunchCard();

    final now = DateTime.now();
    selectedMonth ??= months[now.month - 1];
    final monthIndex = months.indexOf(selectedMonth!) + 1;

    // We'll filter by selectedYear for month-based or other filters
    final year = selectedYear;

    if (filter == 'today') {
      // This uses the device's "today" date. If 'year' != now.year => zero results.
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedIn.year == now.year &&
            pc.clockedIn.month == now.month &&
            pc.clockedIn.day == now.day;
      }).toList();
    } else if (filter == 'week') {
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedIn.isAfter(startOfWeek) &&
            pc.clockedIn.year == year &&
            pc.clockedIn.month == monthIndex;
      }).toList();
    } else if (filter == 'month') {
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedIn.year == year && pc.clockedIn.month == monthIndex;
      }).toList();
    } else if (filter == 'live') {
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedOut == null &&
            pc.id != 'scheduled' &&
            pc.clockedIn.year == year &&
            pc.clockedIn.month == monthIndex;
      }).toList();
    } else if (filter == 'scheduled') {
      filteredPunchCards = punchCards.where((pc) {
        return pc.id == 'scheduled' &&
            pc.clockedIn.year == year &&
            pc.clockedIn.month == monthIndex;
      }).toList();
    } else if (filter == 'completed') {
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedOut != null &&
            pc.id != 'scheduled' &&
            pc.clockedIn.year == year &&
            pc.clockedIn.month == monthIndex;
      }).toList();
    } else if (filter == 'earlyLatePunchIn') {
      filteredPunchCards = punchCards.where((pc) {
        return alerts.any((alert) =>
            alert.punchCardId == pc.id &&
            (alert.alertTypeId == Alert.earlyPunchInId ||
                alert.alertTypeId == Alert.latePunchInId) &&
            alert.alertOn.year == year &&
            alert.alertOn.month == monthIndex);
      }).toList();
    } else if (filter == 'earlyLatePunchOut') {
      filteredPunchCards = punchCards.where((pc) {
        return alerts.any((alert) =>
            alert.punchCardId == pc.id &&
            (alert.alertTypeId == Alert.earlyPunchOutId ||
                alert.alertTypeId == Alert.latePunchOutId) &&
            alert.alertOn.year == year &&
            alert.alertOn.month == monthIndex);
      }).toList();
    } else if (filter == 'geofenceBreach') {
      filteredPunchCards = punchCards.where((pc) {
        return alerts.any((alert) =>
            alert.punchCardId == pc.id &&
            (alert.alertTypeId == Alert.outsideGeofenceId ||
                alert.alertTypeId == Alert.activeBreach) &&
            alert.alertOn.year == year &&
            alert.alertOn.month == monthIndex);
      }).toList();
    } else if (filter == 'noShow') {
      filteredPunchCards = punchCards.where((pc) {
        return alerts.any((alert) =>
            alert.punchCardId == pc.id &&
            alert.alertTypeId == Alert.noPunchInId &&
            alert.alertOn.year == year &&
            alert.alertOn.month == monthIndex);
      }).toList();
    } else {
      // "all"
      filteredPunchCards = punchCards.where((pc) {
        return pc.clockedIn.year == year && pc.clockedIn.month == monthIndex;
      }).toList();
    }

    filteredAlerts = alerts.where((alert) {
      bool belongsToFilteredPunchCard =
          filteredPunchCards.any((pc) => pc.id == alert.punchCardId);
      return belongsToFilteredPunchCard &&
          alert.alertOn.year == year &&
          alert.alertOn.month == monthIndex;
    }).toList();

    recalcAlertCounts(filteredAlerts);
    recalcPunchCardCounts(filteredPunchCards);

    await initializeMarkers();
    notifyListeners();
  }

  Future<void> applyMonthFilter(String month) async {
    print('Applying month filter: $month');
    await resetFirstTablePagination();
    selectedMonth = month;
    selectedFilter = 'month';
    await applyFilter('month');
  }

  Future<void> updateCounts() async {
    final now = DateTime.now();
    final monthIndex = months.indexOf(selectedMonth!) + 1;

    final monthlyAlerts = alerts
        .where(
          (alert) =>
              alert.alertOn.month == monthIndex &&
              alert.alertOn.year == now.year,
        )
        .toList();

    liveCount =
        filteredPunchCards.where((card) => card.clockedOut == null).length;
    scheduledCount =
        filteredPunchCards.where((card) => card.id == 'scheduled').length;
    completedCount =
        filteredPunchCards.where((card) => card.clockedOut != null).length;

    earlyLatePunchInCount = monthlyAlerts
        .where((alert) =>
            alert.alertTypeId == Alert.earlyPunchInId ||
            alert.alertTypeId == Alert.latePunchInId)
        .length;

    earlyLatePunchOutCount = monthlyAlerts
        .where((alert) =>
            alert.alertTypeId == Alert.earlyPunchOutId ||
            alert.alertTypeId == Alert.latePunchOutId)
        .length;

    geofenceBreachCount = monthlyAlerts
        .where((alert) =>
            alert.alertTypeId == Alert.outsideGeofenceId ||
            alert.alertTypeId == Alert.activeBreach)
        .length;

    noShowCount = monthlyAlerts
        .where((alert) => alert.alertTypeId == Alert.noPunchInId)
        .length;

    notifyListeners();
  }

  String get startOfMonth {
    final now = DateTime.now();
    final monthIndex = months.indexOf(selectedMonth!) + 1;
    return DateFormat('MM/dd/yyyy').format(DateTime(now.year, monthIndex, 1));
  }

  String get endOfMonth {
    final now = DateTime.now();
    final monthIndex = months.indexOf(selectedMonth!) + 1;
    return DateFormat('MM/dd/yyyy')
        .format(DateTime(now.year, monthIndex + 1, 0));
  }

  String get selectedEmployeeFullName {
    final user = userMap[selectedEmployeeUserId];
    return user != null ? '${user.firstName} ${user.lastName}' : 'Unknown';
  }

  Future<void> updateCountsAndHours() async {
    final monthIndex = months.indexOf(selectedMonth!) + 1;
    totalCount = punchCards
        .where((card) =>
            card.clockedIn.month == monthIndex &&
            card.clockedIn.year == selectedYear)
        .length;

    final durations = punchCards
        .where((card) =>
            card.clockedIn.month == monthIndex &&
            card.clockedIn.year == selectedYear &&
            card.duration != null)
        .map((card) => card.duration!)
        .toList();

    if (durations.isNotEmpty) {
      final totalMinutes = durations.fold<int>(
          0, (previousValue, duration) => previousValue + duration.inMinutes);

      final hours = totalMinutes ~/ 60;
      final minutes = totalMinutes % 60;
      formattedTotalHours = '${hours}h ${minutes}m';
    } else {
      formattedTotalHours = '0h 0m';
    }
  }

  Future<List<TravelPing>> fetchTravelPings(String punchCardId) async {
    final box = Hive.box<TravelPing>('travelPings');
    final stored =
        box.values.where((p) => p.punchCardId == punchCardId).toList();
    if (stored.isNotEmpty) {
      print(
          'Found ${stored.length} local TravelPings for punchCard $punchCardId');
      stored.forEach((ping) {
        print(
            'Local TravelPing ID: ${ping.id}, PunchCardID: ${ping.punchCardId}, Lat: ${ping.startLat}, Lng: ${ping.startLong}');
      });
      return stored;
    }

    try {
      FetchTravelPingsResponse response =
          await ApiModel().fetchTravelPings(punchCardId);
      final fromApi = response.travelPings;
      print('Fetched ${fromApi.length} TravelPings from API for $punchCardId');

      fromApi.forEach((ping) {
        print(
            'API TravelPing ID: ${ping.id}, PunchCardID: ${ping.punchCardId}, Lat: ${ping.startLat}, Lng: ${ping.startLong}');
      });

      for (final ping in fromApi) {
        await box.put(ping.id, ping);
      }
      return fromApi;
    } catch (e) {
      print('Error fetching TravelPings for $punchCardId => $e');
      return [];
    }
  }
}

class FilterOptionsSheet extends StatelessWidget {
  final _ViewModel viewModel;

  const FilterOptionsSheet({Key? key, required this.viewModel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    String currentFilter = viewModel.selectedFilter;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      decoration: BoxDecoration(
        color: Theme.of(context).canvasColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            "Filter Options",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchRed(),
                ),
          ),
          const SizedBox(height: 20),
          _buildFilterOption(
            context,
            title: 'Live',
            isSelected: currentFilter == 'live',
            onTap: () {
              viewModel.applyFilter('live');
              Navigator.pop(context);
            },
            color: ColorHelper.thePunchBorderGreen(),
          ),
          _buildFilterOption(
            context,
            title: AppLocalization.of(context)!.scheduled,
            isSelected: currentFilter == 'scheduled',
            onTap: () {
              viewModel.applyFilter('scheduled');
              Navigator.pop(context);
            },
            color: ColorHelper.thePunchBlue(),
          ),
          _buildFilterOption(
            context,
            title: 'Completed',
            isSelected: currentFilter == 'completed',
            onTap: () {
              viewModel.applyFilter('completed');
              Navigator.pop(context);
            },
            color: ColorHelper.thePunchRed(),
          ),
          _buildFilterOption(
            context,
            title: AppLocalization.of(context)!.all,
            isSelected: currentFilter == 'all',
            onTap: () {
              viewModel.applyFilter('all');
              Navigator.pop(context);
            },
            color: Colors.grey,
          ),
          const SizedBox(height: 10),
          if (currentFilter != 'all') ...[
            Center(
              child: TextButton(
                onPressed: () {
                  viewModel.applyFilter('all');
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 8.0),
                ),
                child: Text(
                  'Reset Filters',
                  style: TextStyle(
                    color: ColorHelper.thePunchRed(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterOption(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 14.0),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2.0 : 1.0,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [],
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
              color: color,
              size: 24.0,
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16.0,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? color : Colors.grey[800],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
