// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../api/api_model.dart';
import '../../../api/sync_model.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/schedule_template.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/busy_dialog.dart';
import '../../../dialogs/error_dialog.dart';
import '../../../dialogs/web/expanded_employees_dialog.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/logging.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/number_picker.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class AddRepeatingSchedulePage extends StatelessWidget {
  const AddRepeatingSchedulePage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(),
      child: MyScaffold(
        title: AppLocalizations.of(context)!.addRepeatingSchedule,
        body: _Body(),
      ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _BodyHeader(),

              _Row1(),
              _Row2(),
              _Row3(),
              const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Divider()),
              _Row4(),
              Consumer<_ViewModel>(builder: (context, viewModel, _) {
                if (viewModel.scheduleTemplate!.recurringFrequencyId == 2) {
                  return _WeekRow();
                }
                if (viewModel.scheduleTemplate!.recurringFrequencyId == 3) {
                  return _MonthRow();
                }
                return Container();
              }),
            ],
          ),
        ),
      );
}

class _BodyHeader extends StatefulWidget {
  @override
  State<_BodyHeader> createState() => _BodyHeaderState();
}

class _BodyHeaderState extends State<_BodyHeader> {
  @override
  Widget build(BuildContext context) {
    final boldBodyText2 = Theme.of(context)
        .textTheme
        .bodyMedium
        ?.copyWith(fontWeight: FontWeight.w600);

    return IntrinsicHeight(child: Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final text = viewModel.scheduleTemplate
                ?.displayName(context, viewModel.location) ??
            '';
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Stack(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                                 Align(
              alignment: Alignment.centerLeft,
              child:  Text('Add Repeating Schedule', style: Theme.of(context).textTheme.titleLarge)),
             
                    
                    Text(text, style: boldBodyText2)],
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ElevatedButton(
                      onPressed: !viewModel.canSave
                          ? null
                          : () async =>
                              await buildSaveDialog(context, viewModel),
                      child: Text(AppLocalizations.of(context)!.submit),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    ));
  }

  Future<void> buildSaveDialog(BuildContext context, _ViewModel viewModel) =>
      showDialog(
          context: context,
          builder: (context) => BusyDialog(future: () async {
                try {
                  await viewModel.save();
                  await SyncModel().sync();
                  if (!mounted) return;
                  Navigator.of(context).pop();
                } on ApiException catch (e, stack) {
                  await logApiException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) =>
                          ErrorDialog(errorCode: e.errorCode));
                } on Exception catch (e, stack) {
                  await logException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) => const ErrorDialog());
                }
              }));
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final location = viewModel.location;
            final employee = viewModel.employee;
            return IntrinsicHeight(
              child: Flex(
                  direction: Axis.horizontal,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Flexible(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        Text('Employee:', style: Theme.of(context).textTheme.bodySmall),
                        buildEmployee(context, viewModel, employee),
                        ],
                      )),
                    Flexible(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        Text('Location:', style: Theme.of(context).textTheme.bodySmall),
                        buildLocation(context, viewModel, location),
                        ],
                      )),
                  ]),
            );
          },
        ),
      );

  Widget buildEmployee(
          BuildContext context, _ViewModel viewModel, User? employee) =>
      InkWell(
          onTap: () => unawaited(showDialog(
              context: context,
              builder: (context) => ExpandedEmployeesDialog(
                  onSelection: (employeeId) =>
                      viewModel.setEmployee(employeeId)))),
          child: DecoratedText(
            padding: const EdgeInsets.all(8),
            filled: true,
            text: employee?.name ?? '',
            labelText: AppLocalizations.of(context)!.employee,
          ));

  Widget buildLocation(
          BuildContext context, _ViewModel viewModel, Location? location) =>
      InkWell(
          onTap: () => unawaited(showDialog(
              context: context,
              builder: (context) => ExpandedLocationsDialog(
                  onSelection: (locationId) =>
                      viewModel.setLocation(locationId)))),
          child: DecoratedText(
            padding: const EdgeInsets.all(8),
            filled: true,
            text: location?.name ?? '',
            labelText: AppLocalizations.of(context)!.location,
          ));
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) => Flex(
          direction: Axis.horizontal,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Flexible(
              flex: 8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Text('Start Date:', style: Theme.of(context).textTheme.bodySmall),
                InkWell(
                  onTap: () async {
                  final pickedDate = await showDatePicker(
                    context: context,
                    builder: (context, child) => Theme(
                    data: ThemeData.light().copyWith(
                      colorScheme: ColorScheme.light(
                      primary: ColorHelper.thePunchRed(),
                      ),
                    ),
                    child: child!,
                    ),
                    initialDate: viewModel.scheduleTemplate!.startDateLocal,
                    firstDate: DateTime.utc(DateTime.now().year - 20),
                    lastDate: DateTime.utc(DateTime.now().year + 20),
                  );
                  if (pickedDate != null) viewModel.setStartDate(pickedDate);
                  },
                  child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  filled: true,
                  text: viewModel.scheduleTemplate!.startDateLocal
                    .toFormattedDateWithYearNoLocal(locale),
                  labelText: AppLocalizations.of(context)!.startDate,
                  ),
                ),
                ],
              )),
            Flexible(
              flex: 8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Text('End Date:', style: Theme.of(context).textTheme.bodySmall),
                InkWell(
                  onTap: () async {
                  final pickedDate = await showDatePicker(
                    context: context,
                    builder: (context, child) => Theme(
                    data: ThemeData.light().copyWith(
                      // Set the accent color of the app
                      colorScheme: ColorScheme.light(
                      primary: ColorHelper.thePunchRed(),
                      ),
                    ),
                    child: child!,
                    ),
                    initialDate: viewModel.scheduleTemplate!.endDateLocal ??
                      DateTime.now().dateOnly,
                    firstDate: DateTime.utc(DateTime.now().year - 20),
                    lastDate: DateTime.utc(DateTime.now().year + 20),
                  );
                  if (pickedDate != null) viewModel.setEndDate(pickedDate);
                  },
                  child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  filled: true,
                  text: viewModel.scheduleTemplate!.endDateLocal
                      ?.toFormattedDateWithYearNoLocal(locale) ??
                    '',
                  labelText: AppLocalizations.of(context)!.endsBy,
                  suffixIcon: IconButton(
                    onPressed:
                      viewModel.scheduleTemplate!.endDateLocal == null
                        ? null
                        : () => viewModel.clearEndDate(),
                    icon: const Icon(Icons.clear)),
                  ),
                ),
                ],
              )),
          ],
        ),
      ),
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            var startTimeLabel = AppLocalizations.of(context)!.startTime;
            if (viewModel.location?.timeZone != null) {
              startTimeLabel += ' (${viewModel.location?.timeZone})';
            }
            var endTimeLabel = AppLocalizations.of(context)!.endTime;
            if (viewModel.location?.timeZone != null) {
              endTimeLabel += ' (${viewModel.location?.timeZone})';
            }

            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  flex: 10,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    Text('Start Time:', style: Theme.of(context).textTheme.bodySmall),
                    InkWell(
                      onTap: () async {
                      final pickedTime = await showTimePicker(
                        context: context,
                        initialTime: viewModel
                          .scheduleTemplate!.startDateLocal.timeOfDay);
                      if (pickedTime != null) {
                        viewModel.setStartTime(pickedTime.duration);
                      }
                      },
                      child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: viewModel
                        .scheduleTemplate!.startDateLocal.timeOfDay
                        .toFormatted(context),
                      labelText: startTimeLabel,
                      ),
                    ),
                    ],
                  )),
                Flexible(
                  flex: 10,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    Text('End Time:', style: Theme.of(context).textTheme.bodySmall),
                    InkWell(
                      onTap: () async {
                      final pickedTime = await showTimePicker(
                        context: context,
                        initialTime: viewModel.endTimeLocal.timeOfDay,
                      );
                      if (pickedTime != null) {
                        viewModel.setEndTime(pickedTime.duration);
                      }
                      },
                      child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: viewModel.endTimeLocal.timeOfDay
                        .toFormatted(context),
                      labelText: endTimeLabel,
                      ),
                    ),
                    ],
                  )),
                Flexible(
                  flex: 8,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    Text('Duration:', style: Theme.of(context).textTheme.bodySmall),
                    InkWell(
                      onTap: () async {
                      final duration =
                        viewModel.scheduleTemplate!.duration.ceilMinutes;
                      final pickedTime = await showTimePicker(
                        helpText: AppLocalizations.of(context)!.duration,
                        context: context,
                        initialTime: duration.timeOfDay,
                        builder: (context, child) => MediaQuery(
                        data: MediaQuery.of(context)
                          .copyWith(alwaysUse24HourFormat: true),
                        child: child!,
                        ),
                      );
                      if (pickedTime != null) {
                        viewModel.setDuration(pickedTime.duration);
                      }
                      },
                      child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: viewModel.scheduleTemplate!.duration.toFormatted,
                      labelText: AppLocalizations.of(context)!.duration,
                      ),
                    ),
                    ],
                  )),
              ],
            );
          },
        ),
      );
}

class _Row4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
      child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) => Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalization.of(context).repeatEvery,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalization.of(context).frequency,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 4),
                            NumberPicker(
                                value: viewModel
                                    .scheduleTemplate!.recurringFrequencyEveryNFreq,
                                minValue: 1,
                                onChanged: (value) {
                                  viewModel.setFrequency(value);
                                }),
                          ],
                        ),
                        const SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalization.of(context).every,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 4),
                            DropdownButton<int>(
                              onChanged: (value) {
                                if (value == null) return;
                                viewModel.setRecurringFrequencyId(value);
                              },
                              value: viewModel.scheduleTemplate!.recurringFrequencyId,
                              items: [
                                DropdownMenuItem(
                                    value: 1,
                                    child: Text(viewModel.scheduleTemplate!
                                                .recurringFrequencyEveryNFreq ==
                                            1
                                        ? AppLocalization.of(context).day
                                        : AppLocalization.of(context).days)),
                                DropdownMenuItem(
                                    value: 2,
                                    child: Text(viewModel.scheduleTemplate!
                                                .recurringFrequencyEveryNFreq ==
                                            1
                                        ? AppLocalization.of(context).week
                                        : AppLocalization.of(context).weeks)),
                                DropdownMenuItem(
                                    value: 3,
                                    child: Text(viewModel.scheduleTemplate!
                                                .recurringFrequencyEveryNFreq ==
                                            1
                                        ? AppLocalization.of(context).month
                                        : AppLocalization.of(context).months)),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              )));
}

class _WeekRow extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
      builder: (context, viewModel, _) => Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalization.of(context).weekday,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Container(
                  constraints: const BoxConstraints(maxWidth: 780),
                  child: DecoratedContainer(
                    child: Center(
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          FilterChip(
                              label: Text(AppLocalization.of(context).monday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnMonday,
                              onSelected: viewModel.setMonday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).tuesday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnTuesday,
                              onSelected: viewModel.setTuesday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).wednesday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnWednesday,
                              onSelected: viewModel.setWednesday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).thursday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnThursday,
                              onSelected: viewModel.setThursday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).friday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnFriday,
                              onSelected: viewModel.setFriday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).saturday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnSaturday,
                              onSelected: viewModel.setSaturday),
                          FilterChip(
                              label: Text(AppLocalization.of(context).sunday),
                              selected: viewModel.scheduleTemplate!
                                  .recurringFrequencyWeeklyOnSunday,
                              onSelected: viewModel.setSunday),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ));
}

class _MonthRow extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
      builder: (context, viewModel, _) => Column(
            children: [
              buildSelection(context, viewModel),
              if (viewModel.onSpecificMonth)
                buildSpecificMonth(context, viewModel),
              if (viewModel.onSpecificWeek)
                buildSpecificWeek(context, viewModel),
              if (viewModel.onSpecificDay) buildSpecificDay(context, viewModel),
            ],
          ));

  Widget buildSelection(BuildContext context, _ViewModel viewModel) => Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalization.of(context).month,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                FilterChip(
                    label: Text(AppLocalization.of(context).onSpecificMonth),
                    selected: viewModel.onSpecificMonth,
                    onSelected: (value) => viewModel.setOnSpecificMonth()),
                FilterChip(
                    label: Text(AppLocalization.of(context).onSpecificWeek),
                    selected: viewModel.onSpecificWeek,
                    onSelected: (value) => viewModel.setOnSpecificWeek()),
                FilterChip(
                    label: Text(AppLocalization.of(context).onSpecificDay),
                    selected: viewModel.onSpecificDay,
                    onSelected: (value) => viewModel.setOnSpecificDay()),
              ],
            ),
          ],
        ),
      );

  Widget buildSpecificMonth(BuildContext context, _ViewModel viewModel) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text('${AppLocalization.of(context).month}:',
                      style: Theme.of(context).textTheme.titleMedium),
                ),
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value == null) return;
                    viewModel.setRecurringFrequencyMonthlyOnMonth(value);
                  },
                  value: viewModel
                      .scheduleTemplate!.recurringFrequencyMonthlyOnMonth,
                  items: [
                    DropdownMenuItem(
                        value: 1,
                        child: Text(AppLocalization.of(context).january)),
                    DropdownMenuItem(
                        value: 2,
                        child: Text(AppLocalization.of(context).february)),
                    DropdownMenuItem(
                        value: 3,
                        child: Text(AppLocalization.of(context).march)),
                    DropdownMenuItem(
                        value: 4,
                        child: Text(AppLocalization.of(context).april)),
                    DropdownMenuItem(
                        value: 5, child: Text(AppLocalization.of(context).may)),
                    DropdownMenuItem(
                        value: 6,
                        child: Text(AppLocalization.of(context).june)),
                    DropdownMenuItem(
                        value: 7,
                        child: Text(AppLocalization.of(context).july)),
                    DropdownMenuItem(
                        value: 8,
                        child: Text(AppLocalization.of(context).august)),
                    DropdownMenuItem(
                        value: 9,
                        child: Text(AppLocalization.of(context).september)),
                    DropdownMenuItem(
                        value: 10,
                        child: Text(AppLocalization.of(context).october)),
                    DropdownMenuItem(
                        value: 11,
                        child: Text(AppLocalization.of(context).november)),
                    DropdownMenuItem(
                        value: 12,
                        child: Text(AppLocalization.of(context).december)),
                  ],
                )
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text('${AppLocalization.of(context).dayOfMonth}:',
                      style: Theme.of(context).textTheme.titleMedium),
                ),
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value == null) return;
                    viewModel.setRecurringFrequencyMonthlyOnDay(value);
                  },
                  value: viewModel
                      .scheduleTemplate!.recurringFrequencyMonthlyOnDay,
                  items: [
                    for (var i = 1; i <= viewModel.daysInMonthlyOnMonth; i++)
                      DropdownMenuItem(
                          value: i, child: Text(i.toOrdinal(context)))
                  ],
                )
              ],
            ),
          ),
        ],
      );

  Widget buildSpecificWeek(BuildContext context, _ViewModel viewModel) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text('${AppLocalization.of(context).week}:',
                      style: Theme.of(context).textTheme.titleMedium),
                ),
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value == null) return;
                    viewModel.setRecurringFrequencyMonthlyTheWeekId(value);
                  },
                  value: viewModel
                      .scheduleTemplate!.recurringFrequencyMonthlyTheWeekId,
                  items: [
                    DropdownMenuItem(
                        value: 4,
                        child:
                            Text(AppLocalization.of(context).firstOccurrence)),
                    DropdownMenuItem(
                        value: 5,
                        child:
                            Text(AppLocalization.of(context).secondOccurrence)),
                    DropdownMenuItem(
                        value: 6,
                        child:
                            Text(AppLocalization.of(context).thirdOccurrence)),
                    DropdownMenuItem(
                        value: 7,
                        child:
                            Text(AppLocalization.of(context).fourthOccurrence)),
                    DropdownMenuItem(
                        value: 8,
                        child:
                            Text(AppLocalization.of(context).lastOccurrence)),
                  ],
                )
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text('${AppLocalization.of(context).day}:',
                      style: Theme.of(context).textTheme.titleMedium),
                ),
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value == null) return;
                    viewModel
                        .setRecurringFrequencyMonthlyTheDayOfTheWeekId(value);
                  },
                  value: viewModel.scheduleTemplate!
                      .recurringFrequencyMonthlyTheDayOfTheWeekId,
                  items: [
                    DropdownMenuItem(
                        value: 11,
                        child: Text(AppLocalization.of(context).monday)),
                    DropdownMenuItem(
                        value: 12,
                        child: Text(AppLocalization.of(context).tuesday)),
                    DropdownMenuItem(
                        value: 13,
                        child: Text(AppLocalization.of(context).wednesday)),
                    DropdownMenuItem(
                        value: 14,
                        child: Text(AppLocalization.of(context).thursday)),
                    DropdownMenuItem(
                        value: 15,
                        child: Text(AppLocalization.of(context).friday)),
                    DropdownMenuItem(
                        value: 9,
                        child: Text(AppLocalization.of(context).saturday)),
                    DropdownMenuItem(
                        value: 10,
                        child: Text(AppLocalization.of(context).sunday)),
                  ],
                )
              ],
            ),
          ),
        ],
      );

  Widget buildSpecificDay(BuildContext context, _ViewModel viewModel) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text('${AppLocalization.of(context).dayOfMonth}:',
                    style: Theme.of(context).textTheme.titleMedium),
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyMonthlyOnDay(value);
                },
                value:
                    viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnDay,
                items: [
                  for (var i = 1; i <= 31; i++)
                    DropdownMenuItem(
                        value: i, child: Text(i.toOrdinal(context)))
                ],
              )
            ],
          ),
          if (viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnDay > 28)
            Text(
                'On months with less than 29 days, the schedule is set to the last day of the month.',
                style: Theme.of(context).textTheme.bodySmall)
        ],
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Duration get endTimeLocal => scheduleTemplate!.startDateLocal
      .add(scheduleTemplate!.duration)
      .timeOfDay
      .duration;

  bool get onSpecificMonth =>
      scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth;
  bool get onSpecificWeek =>
      !scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth &&
      !scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay;
  bool get onSpecificDay =>
      scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay;

  Location? location;
  User? employee;
  ScheduleTemplate? scheduleTemplate;

  _ViewModel() {
    addListenables([
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }
  @override
  Future<void> refresh() async {
    scheduleTemplate = ScheduleTemplate.create();

    notifyListeners();
  }

  Future<void> save() => ApiModel().addScheduleTemplate(scheduleTemplate!);

  bool get canSave {
    if (scheduleTemplate!.userId.isEmpty) return false;
    if (scheduleTemplate!.locationId.isEmpty) return false;
    if (!validateWeekday()) return false;
    return true;
  }

  Future<void> setEmployee(String id) async {
    scheduleTemplate!.userId = id;
    employee = await DataModel().userModel.getById(id);
    notifyListeners();
  }

  Future<void> setLocation(String id) async {
    scheduleTemplate!.locationId = id;
    location = await DataModel().locationModel.getById(id);
    notifyListeners();
  }

  void setStartDate(DateTime value) {
    final time = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.startDateLocal = DateTime.utc(
        value.year, value.month, value.day, time.hour, time.minute);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setEndDate(DateTime value) {
    final time = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.endDateLocal = DateTime.utc(
        value.year, value.month, value.day, time.hour, time.minute);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void clearEndDate() {
    scheduleTemplate!.endDateLocal = null;
    notifyListeners();
  }

  void setStartTime(Duration value) {
    final date = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.startDateLocal = DateTime.utc(
        date.year, date.month, date.day, value.inHours, value.inMinutes % 60);
    notifyListeners();
  }

  void setEndTime(Duration value) {
    var duration = value - scheduleTemplate!.startDateLocal.timeOfDay.duration;
    if (duration < Duration.zero) {
      duration = duration + const Duration(hours: 24);
    }
    if (duration > const Duration(hours: 24)) {
      duration = duration - const Duration(hours: 24);
    }
    scheduleTemplate!.duration = duration;
    notifyListeners();
  }

  void setDuration(Duration value) {
    scheduleTemplate!.duration = value;
    notifyListeners();
  }

  void setFrequency(int value) {
    scheduleTemplate!.recurringFrequencyEveryNFreq = value;
    notifyListeners();
  }

  void setRecurringFrequencyId(int value) {
    scheduleTemplate!.recurringFrequencyId = value;
    notifyListeners();
  }

  void setMonday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnMonday = value;
    notifyListeners();
  }

  void setTuesday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnTuesday = value;
    notifyListeners();
  }

  void setWednesday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnWednesday = value;
    notifyListeners();
  }

  void setThursday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnThursday = value;
    notifyListeners();
  }

  void setFriday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnFriday = value;
    notifyListeners();
  }

  void setSaturday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnSaturday = value;
    notifyListeners();
  }

  void setSunday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnSunday = value;
    notifyListeners();
  }

  void setOnSpecificMonth() {
    if (scheduleTemplate!.recurringFrequencyMonthlyOnDay >
        daysInMonthlyOnMonth) {
      scheduleTemplate!.recurringFrequencyMonthlyOnDay = daysInMonthlyOnMonth;
    }
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = true;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = false;
    notifyListeners();
  }

  void setOnSpecificWeek() {
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = false;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = false;
    notifyListeners();
  }

  void setOnSpecificDay() {
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = false;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = true;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyTheWeekId(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyTheWeekId = value;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyTheDayOfTheWeekId(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyTheDayOfTheWeekId = value;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyOnMonth(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyOnMonth = value;
    if (scheduleTemplate!.recurringFrequencyMonthlyOnDay >
        daysInMonthlyOnMonth) {
      scheduleTemplate!.recurringFrequencyMonthlyOnDay = daysInMonthlyOnMonth;
    }
    notifyListeners();
  }

  int get daysInMonthlyOnMonth {
    if (scheduleTemplate!.recurringFrequencyMonthlyOnMonth == 2) return 28;
    if (scheduleTemplate!.recurringFrequencyMonthlyOnMonth % 2 == 0) return 30;
    return 31;
  }

  void setRecurringFrequencyMonthlyOnDay(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyOnDay = value;
    notifyListeners();
  }

  bool validateWeekday() {
    if (scheduleTemplate!.recurringFrequencyId != 2) return true;
    return scheduleTemplate!.recurringFrequencyWeeklyOnMonday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnTuesday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnWednesday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnThursday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnFriday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnSaturday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnSunday;
  }
}
