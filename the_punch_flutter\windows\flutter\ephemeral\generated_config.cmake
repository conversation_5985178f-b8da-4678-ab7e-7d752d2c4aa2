# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.19.6" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\code\\punch\\the_punch_flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.1.8+113" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 8 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 113 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.19.6"
  "PROJECT_DIR=C:\\Users\\<USER>\\code\\punch\\the_punch_flutter"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.19.6"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\code\\punch\\the_punch_flutter\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\code\\punch\\the_punch_flutter"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\code\\punch\\the_punch_flutter\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jNGNkNDhlMTg2NDYwYjMyZDQ0NTg1Y2UzYzEwMzI3MWFiNjc2MzU1Lw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\code\\punch\\the_punch_flutter\\.dart_tool\\package_config.json"
)
