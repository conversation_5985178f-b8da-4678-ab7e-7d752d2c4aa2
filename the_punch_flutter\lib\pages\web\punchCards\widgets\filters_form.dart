import '../../../../helpers/color_helper.dart';
import '../../../../widgets/decorated_text_field.dart';
import '../punch_cards.dart';
import 'package:flutter/material.dart';
import '../../../../misc/extensions.dart';
import 'SearchBarComponent_DropdownFiltersComponent.dart';
import 'DatePickerComponent.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class FiltersForm extends StatefulWidget {
  final ViewModelPunch viewModel;

  const FiltersForm({super.key, required this.viewModel});

  @override
  State<FiltersForm> createState() => _FiltersFormState();
}

class _FiltersFormState extends State<FiltersForm> {
  _TimePeriod timePeriod = _TimePeriod.thisWeek;

  @override
  Widget build(BuildContext context) => 
      Align(
        alignment: Alignment.centerLeft,
        child: Container(
          width: 1133,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: const Color(0xFFebf6ff),
            borderRadius: BorderRadius.circular(32),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 2,
                    child:
                     SearchBarComponent_DropdownFiltersComponent(viewModel: widget.viewModel),
                  ),
                  const SizedBox(width: 32),
                  Expanded(
                    flex: 1,
                    child: buildTimePeriodDropdown(widget.viewModel, context),
                  ),
                  if (timePeriod == _TimePeriod.singleDay)
                    buildSingleDayPicker(context, widget.viewModel),
                    
                  const Expanded(
                    flex: 2,
                    child: DatePickerComponent(),
                  ),
                  
                ],
              ),
            ],
          ),
        ),
      );

  Widget buildSingleDayPicker(BuildContext context, ViewModelPunch viewModel) {
    final locale = Localizations.localeOf(context);
    final dateString = viewModel.start.toLongFormattedDate(locale);

    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          builder: (context, child) => Theme(
            data: ThemeData.light().copyWith(
              colorScheme: ColorScheme.light(primary: ColorHelper.thePunchRed()),
            ),
            child: child!,
          ),
          initialDate: viewModel.start,
          firstDate: DateTime.utc(DateTime.now().year - 20),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (picked != null) {
          await viewModel.setTimePeriod(picked, picked);
        }
      },
      child: DecoratedText(
        text: dateString,
        labelText: "Single Day",
      ),
    );
  }

  Widget buildTimePeriodDropdown(ViewModelPunch viewModel, BuildContext context) =>
    DropdownButtonFormField<_TimePeriod>(
      icon: const Icon(Icons.arrow_drop_down_rounded, color: Colors.white),
      style: const TextStyle(color: Colors.white),
      dropdownColor: ColorHelper.thePunchDarkBlue(),
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: ColorHelper.thePunchDarkBlue(),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.transparent,
            width: 2.0,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        labelStyle: const TextStyle(
          color: Colors.white,
        ),
      ),
      value: timePeriod,
      onChanged: (value) async {
        if (value == null) return;
        
        setState(() {
          timePeriod = value;
        });

        final now = DateTime.now();
        DateTime start, end;
        
        switch (value) {
          case _TimePeriod.today:
            start = DateTime(now.year, now.month, now.day);
            end = start;
            break;
          case _TimePeriod.yesterday:
            start = DateTime(now.year, now.month, now.day - 1);
            end = start;
            break;
          case _TimePeriod.thisWeek:
            start = now.subtract(Duration(days: now.weekday - 1));
            end = now;
            break;
          case _TimePeriod.lastWeek:
            start = now.subtract(Duration(days: now.weekday + 6));
            end = now.subtract(Duration(days: now.weekday));
            break;
          case _TimePeriod.thisMonth:
            start = DateTime(now.year, now.month, 1);
            end = now;
            break;
          case _TimePeriod.lastMonth:
            start = DateTime(now.year, now.month - 1, 1);
            end = DateTime(now.year, now.month, 0);
            break;
          case _TimePeriod.all:
            start = DateTime(2000);
            end = now;
            break;
          default:
            return;
        }
        
        await viewModel.setTimePeriod(start, end);
      },
      items: [
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.today,
          child: Text(AppLocalizations.of(context)!.today, style: const TextStyle(color: Color.fromARGB(255, 0, 0, 0))),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.yesterday,
          child: Text(AppLocalizations.of(context)!.yesterday, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.thisWeek,
          child: Text(AppLocalizations.of(context)!.thisWeek, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.lastWeek,
          child: Text(AppLocalizations.of(context)!.lastWeek, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.thisMonth,
          child: Text(AppLocalizations.of(context)!.thisMonth, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.lastMonth,
          child: Text(AppLocalizations.of(context)!.lastMonth, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.all,
          child: Text(AppLocalizations.of(context)!.allPunchCards, style: const TextStyle(color: Colors.white)),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.custom,
          child: Text(AppLocalizations.of(context)!.customTimePeriod, style: const TextStyle(color: Colors.white)),
        ),
        const DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.singleDay,
          child: Text('Single Day', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
}


enum _TimePeriod {
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  all,
  custom,
  singleDay,
}
