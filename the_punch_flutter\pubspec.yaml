name: the_punch_flutter
description: The Punch
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.1.8+113

environment:
  sdk: ">=2.19.0 <3.0.0"

dependencies:
  after_layout: ^1.1.0
  android_intent_plus: ^5.0.2
  async: ^2.6.1
  basics: ^0.10.0
  camera: ^0.10.0+1
  collection: ^1.15.0
  community_material_icon: ^5.9.55
  connectivity_plus: ^4.0.2
  csv: ^5.0.1
  dartx: ^1.0.0
  expandable: ^5.0.1
  file_picker: ^5.0.1
  firebase_analytics: ^10.4.5
  firebase_core: ^2.15.1
  firebase_crashlytics: ^3.3.5
  firebase_messaging: ^14.0.1
  flutter:
    sdk: flutter
  flutter_background_geolocation: ^4.15.5
  flutter_background_service: ^5.0.5
  flutter_background_service_android: ^6.2.2
  flutter_foreground_task: ^6.4.0
  flutter_image_compress: ^2.0.4
  flutter_local_notifications: ^17.2.1+2
  flutter_localizations:
    sdk: flutter
  flutter_localized_locales: ^2.0.1
  flutter_polyline_points: ^2.0.0
  flutter_secure_storage: ^8.0.0
  rxdart: ^0.28.0

  flutter_web_plugins:
    sdk: flutter
  font_awesome_flutter: ^10.1.0
  geocoding: ^3.0.0
  geolocator: ^10.0.0
  go_router: ^10.0.0
  google_fonts: ^6.1.0
  google_maps_flutter: ^2.2.1
  google_maps_flutter_web: ^0.5.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.1.0
  http_parser: ^4.0.1
  image_picker: ^1.1.2
  intl: ^0.18.1
  json_annotation: ^4.5.0
  maps_launcher: ^2.0.1
  material_symbols_icons: ^4.2805.1
  package_info_plus: ^4.1.0
  path: ^1.8.1
  path_provider: ^2.0.1
  permission_handler: ^11.3.1
  printing: ^5.11.0
  provider: ^6.0.2
  scrollable_positioned_list: ^0.3.2
  separated_column: ^2.0.0
  shared_preferences: ^2.0.15
  system_clock: ^2.0.0
  table_calendar: ^3.0.9
  timezone: ^0.9.2
  tuple: ^2.0.0
  universal_html: ^2.0.8
  url_launcher: ^6.0.3
  url_strategy: ^0.2.0
  uuid: ^3.0.4
  vector_math: ^2.1.2
  visibility_detector: ^0.4.0+2

dev_dependencies:
  build_runner: ^2.1.11
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  integration_test:
    sdk: flutter
  json_serializable: ^6.2.0
  intl_utils: ^2.8.7


flutter:
  uses-material-design: true
  generate: true
  assets:
    - images/the_punch_logo_small.png
    - images/Vector.png
    - images/custom_location.png # Ensure this path matches the actual file location
    - images/circle.png # Ensure this path matches the actual file location
    - assets/map_style.json      # Ensure this path matches the actual file location
  fonts:
    - family: ThePunchIcons
      fonts:
        - asset: fonts/ThePunchIcons.ttf


flutter_icons:
  android: true
  ios: false
  image_path: "images/the_punch_logo2.png"
