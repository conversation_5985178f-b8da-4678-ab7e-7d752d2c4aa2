import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../helpers/color_helper.dart';
import '../../misc/extensions.dart';
import 'calendar_widget.dart';
import '../menu/aligned_popup_menu_button.dart';

class CalendarWeekWidget extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;
  final Widget suffix;
  final double height;

  const CalendarWeekWidget({
    super.key,
    required this.builder,
    required this.suffix,
    this.scrollable = true,
    required this.height,
  });

  @override
  Widget build(BuildContext context) =>
  Column(
        children: [
          //_Header(suffix: suffix),
          Flexible(child: _Body(builder: builder, scrollable: scrollable)),
        ],
      );
}

class _Header extends StatelessWidget {
  final Widget suffix;

  const _Header({required this.suffix});

  @override
  Widget build(BuildContext context) => Consumer<CalendarViewModel>(
        builder: (context, viewModel, _) => Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CalendarHeaderPrefix(
              previousPage: () async => await viewModel.goToPreviousWeek(context),
              nextPage: () async => await viewModel.goToNextWeek(context),
              today: () => viewModel.setDate(DateTime.now().dateOnly),
            ),
            DropdownButton<DateTime>(
              onChanged: (value) {
                if (value == null || viewModel.date == value) return;
                viewModel.setDate(value);
              },
              value: viewModel.date.startOfWeek,
              items: [
                for (var i = -5; i <= 5; i++)
                  DropdownMenuItem<DateTime>(
                      value: viewModel.date.startOfWeek.addDays(i * 7),
                      child: Text(formattedInterval(
                          context, viewModel.date.startOfWeek.addDays(i * 7)))),
              ],
            ),
            suffix,
          ],
        ),
      );

  String formattedInterval(BuildContext context, DateTime week) {
    final locale = Localizations.localeOf(context);

    return '${week.toFormattedDate(locale)}  - ${week.addDays(6).toFormattedDate(locale)}';
  }
}

class _Body extends StatefulWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;

  const _Body({required this.builder, this.scrollable = true});

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  static const _initialPage = 1073741824;
  final _controller = PageController(initialPage: _initialPage);
  late final DateTime _initialWeek = DateTime.now().startOfWeek;

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      if (_controller.page != null &&
          _controller.page!.toInt() == _controller.page) {
        final offset = _controller.page!.toInt() - _initialPage;
        final week = _initialWeek.addDays(offset * 7);
        final viewModel = context.read<CalendarViewModel>();
        if (viewModel.date.startOfWeek != week) viewModel.setDate(week);
      }
    });
  }

  @override
  Widget build(BuildContext context) =>
      Consumer<CalendarViewModel>(builder: (context, viewModel, _) {
        if (_controller.hasClients) {
          final offset =
              (viewModel.date.startOfWeek.difference(_initialWeek).inHours /
                      (24 * 7))
                  .round();
          final page = _initialPage + offset;
          if (page != _controller.page) {
            if ((page - _controller.page!).abs() > 2) {
              _controller.jumpToPage(page);
            } else {
              unawaited(_controller.animateToPage(page,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.ease));
            }
          }
        }
        return PageView.builder(
            physics:
                widget.scrollable ? null : const NeverScrollableScrollPhysics(),
            controller: _controller,
            itemBuilder: (context, index) {
              final offset = index - _initialPage;
              final week = _initialWeek.addDays(offset * 7);
              return _Page(week: week, builder: widget.builder);
            });
      });
}

class _Page extends StatelessWidget {
  final DateTime week;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final _controller = ScrollController(
      initialScrollOffset: (CalendarViewModel.pageHeight / 48) * 18);

  _Page({required this.week, required this.builder});

  @override
  Widget build(BuildContext context) =>
    SingleChildScrollView(
        //crossAxisAlignment: CrossAxisAlignment.start,
        child:
          _DatesRow(week, this.builder),

             );
}

class _DatesRow extends StatelessWidget {
  final DateTime week;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;

  const _DatesRow(this.week, this.builder);

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final dates = <DateTime>[];
          for (var i = 0; i < 7; i++) {
            dates.add(DateUtils.addDaysToDate(week, i));
          }

          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;
          final columnWidth = width / dates.length;

            return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              for (var i = 0; i < dates.length; i++)
                Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blue[100]!),
                ),
                child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Expanded(
                  flex: 1,
                  child: SizedBox(
                  width: columnWidth,

                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(width: 10,),
                      Text(
                      dates[i].day.toString(),
                      style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 15.0,horizontal: 15),
                      child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                        '${DateFormat('MMMM').format(dates[i])}, ${DateFormat('yyyy').format(dates[i])}',
                        style: const TextStyle(fontSize: 12),
                        ),
                        Text(
                        DateFormat('EEEE').format(dates[i]),
                        style: const TextStyle(fontSize: 12),
                        ),
                      ],
                      ),
                    ),
                  ],
                  ),

                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _DailyTotals(date: dates[i]),
                    Column(children:[ 
                    _Events2(dates[i], builder),]
                    ),
                    ]
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _DailyTotals2(date: dates[i], builder: builder),
                     _Events(dates[i], builder)
                    ]
                  ),
                ),


                ],
              ))
            ],
            );
        },
      );
  List<Widget> buildColumnTitles() {
    final columns = <Widget>[];
    for (var i = 0; i < 7; i++) {
      final date = DateUtils.addDaysToDate(week, i);
      columns.add(Text(
          '${DateFormat(DateFormat.ABBR_WEEKDAY).format(date)} ${DateFormat(DateFormat.ABBR_MONTH_DAY).format(date)}'));
    }
    return columns;
  }

}

class _DailyTotals extends StatelessWidget {
  final DateTime date;

  const _DailyTotals({required this.date});

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;
          final columnWidth = width / 7;

          // Remove Positioned and just return the Container directly
          return Container(
            color: Colors.transparent,
            padding: const EdgeInsets.all(0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                  ),
                  child: Text(
                    'Daily Totals',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                ),

              ],
            ),
          );
        },
      );
}
class _DailyTotals2 extends StatelessWidget {
  final DateTime date;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;

  const _DailyTotals2({required this.date, required this.builder});

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;
          final columnWidth = width / 7;
          final start = date.addDays(0);
          final end = date.addDays(1);

          // Get total hours from calendar events
          final eventsFuture = builder(context, start, end);

          // Use FutureBuilder to handle the async operation properly
          return FutureBuilder<Iterable<CalendarEvent>>(
            future: eventsFuture,
            builder: (context, snapshot) {
              // Calculate total hours if needed
             var totalHours = 0.0; if (snapshot.hasData) {
                // Uncomment and use this code if you want to display the actual calculated hours
                
                
                for (final event in snapshot.data!) {
                  totalHours += event.end.difference(event.start).inHours;
                }
                // Use totalHours to update the display text if needed
                // Example: 'Total: ${totalHours}h'
                
              }

              // Return the container with appropriate text
              return Container(
                padding: EdgeInsets.zero,
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                      ),
                      child: Text(
                        'Total Hours: $totalHours',
                        style: TextStyle(fontSize: 14,color: Colors.black ,fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      );
}
class _EmployeeEvent extends StatelessWidget {
  final String hours;
  final String timeRange;
  final String name;
  final String id;
  final String location;

  const _EmployeeEvent({
    required this.hours,
    required this.timeRange,
    required this.name,
    required this.id,
    required this.location,
  });

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(minHeight: 56),
        decoration: BoxDecoration(
          color: Colors.white,
         border: Border.all(color: Colors.blue[50]!),
         // borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            Row(
              children: [
                Text(
                  hours,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  timeRange,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),

          ],
        ),
      );
}

class _EmployeeEvent2 extends StatelessWidget {
  final String hours;
  final String timeRange;
  final String name;
  final String id;
  final String location;

  const _EmployeeEvent2({
    required this.hours,
    required this.timeRange,
    required this.name,
    required this.id,
    required this.location,
  });

  @override
  Widget build(BuildContext context) => Container(
        constraints: BoxConstraints(minHeight: 44),
        //height: 34,
        padding: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.blue[50]!),
         // borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [

            
            Text(
                '${location.split("\n")[0]} ',
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.black),
            ),
            Text(
              '${location.split("\n")[1]}',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      );
}

class _HoursColumn extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final times = <String>[];
          for (var i = 0; i < 48; i++) {
            final hour = i ~/ 2;
            final minute = (i % 2) * 30;
            final date = DateTime.now()
                .setTimeOfDay(TimeOfDay(hour: hour, minute: minute));
            times.add(DateFormat(DateFormat.HOUR_MINUTE).format(date));
          }

          final height = constraints.maxHeight -
              (constraints.maxHeight / times.length) / 2;

          return Stack(
            children: [
              for (var i = 0; i < times.length; i++)
                Positioned(
                  top: height / times.length * i + (height / times.length) / 2,
                  left: 8,
                  child: Text(times[i]),
                ),
            ],
          );
        },
      );
}

class _HoizontalDividers extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const rows = 48;

          final height =
              constraints.maxHeight - (constraints.maxHeight / rows) / 2;

          return Stack(
            children: [
              for (var i = 2; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(31),
                  ),
                ),
              for (var i = 1; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(10),
                  ),
                ),
            ],
          );
        },
      );
}

class _VerticalDividers extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const columns = 7;

          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;

          return Stack(
            children: [
              for (var i = 0; i < columns; i += 1)
                Positioned(
                  top: 8,
                  bottom: 8,
                  left: (width / columns) * i + timesWidth,
                  child: const VerticalDivider(
                      width: 1, thickness: 1, indent: 0, endIndent: 0),
                ),
            ],
          );
        },
      );
}

class _Events extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final DateTime week;

  const _Events(this.week, this.builder);

  @override
  Widget build(BuildContext context) {
    final start = week.addDays(0);
    final end = week.addDays(1);

    return FutureBuilder<Iterable<CalendarEvent>>(
      future: Future(() async => await builder(context, start, end)),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        final events = snapshot.data!.toList();
        if (events.isEmpty) return Container();

        final splitEvents = <CalendarEvent>[];
        for (final event in events) {
          if (event.start.toLocal().timeOfDay.duration >
              event.end.toLocal().timeOfDay.duration) {
            final splitEvent = CalendarEvent.from(event);
            event.splitEnd =
                splitEvent.splitStart = event.end.toLocal().dateOnly;
            splitEvents.add(splitEvent);
          }
        }
        events.addAll(splitEvents);

        // group events by date then group events that overlapping times
        final eventGroups = [<List<CalendarEvent>>[]];
        for (final event in events) {
          var added = false;
          for (final eventGroup in eventGroups[0]) {
            if (eventGroup.any((e) =>
                e.splitStart <= event.splitEnd &&
                e.splitEnd > event.splitStart)) {
              eventGroup.add(event);
              added = true;
              break;
            }
          }
          if (!added) eventGroups[0].add([event]);
        }

        // display each date with overlapping getting 1/2 column width (or 1/3 if there's 3 overlapping, etc.)
        return Container(
          width: double.infinity,
          
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (final eventGroup in eventGroups[0])
          for (var j = 0; j < eventGroup.length; j++)
            _EventTile2(eventGroup[j]),
            ],
          ),
        );
      },
    );
  }
}


class _Events2 extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final DateTime week;

  const _Events2(this.week, this.builder);

  @override
  Widget build(BuildContext context) {
    final start = week.addDays(0);
    final end = week.addDays(1);

    return FutureBuilder<Iterable<CalendarEvent>>(
      future: Future(() async => await builder(context, start, end)),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        final events = snapshot.data!.toList();
        if (events.isEmpty) return Container();

        final splitEvents = <CalendarEvent>[];
        for (final event in events) {
          if (event.start.toLocal().timeOfDay.duration >
              event.end.toLocal().timeOfDay.duration) {
            final splitEvent = CalendarEvent.from(event);
            debugPrint('EVENTS ${splitEvent.start}');
            event.splitEnd =
                splitEvent.splitStart = event.end.toLocal().dateOnly;
            splitEvents.add(splitEvent);
          }
        }
        events.addAll(splitEvents);

        // group events by date then group events that overlapping times
        final eventGroups = [<List<CalendarEvent>>[]];
        for (final event in events) {
          var added = false;
          for (final eventGroup in eventGroups[0]) {
            if (eventGroup.any((e) =>
                e.splitStart <= event.splitEnd &&
                e.splitEnd > event.splitStart)) {
              eventGroup.add(event);
              added = true;
              break;
            }
          }
          if (!added) eventGroups[0].add([event]);
        }

        // display each date with overlapping getting 1/2 column width (or 1/3 if there's 3 overlapping, etc.)
        return  // Adjust this height as needed
           Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (final eventGroup in eventGroups[0])
          for (var j = 0; j < eventGroup.length; j++)
            _EventTile(eventGroup[j]),
            ],
          );
        
      },
    );
  }
}

class _EventTile extends StatelessWidget {
  final CalendarEvent event;

  const _EventTile(this.event);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final start = event.start.toFormattedTime(locale);
    final end = event.end.toFormattedTime(locale);
    final diff = event.end.difference(event.start).inHours;
    Widget child = Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue[50]!),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal:  8 ,vertical: 4),
        child: SizedBox(
          height: 34, // Set a fixed height or adjust as needed
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  '${diff}h • $start - $end',
                  style: event.textStyle!.copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.bold
                  )
                )
              ),
            ],
          ),
        )
      )
    );
    if (event.onTap != null) {
      child = InkWell(
        onTap: event.onTap,
        child: child,
      );
    }

    if (event.popupItemBuilder != null) {
      child = AlignedPopupMenuButton<int>(
        itemBuilder: event.popupItemBuilder!,
        onSelected: event.popupOnSelected,
        child: child,
      );
    }

    if (event.tooltip != null) {
      child = Tooltip(
          message: event.tooltip,
          textStyle: event.tooltipTextStyle,
          child: child);
    }
    return child;
  }
}


class _EventTile2 extends StatelessWidget {
  final CalendarEvent event;

  const _EventTile2(this.event);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final start = event.start.toFormattedTime(locale);
    final end = event.end.toFormattedTime(locale);
    final diff = event.end.difference(event.start).inHours;
    Widget child = _EmployeeEvent2(
      hours: '${diff}h',
      timeRange: '$start - $end',
      name: 'John Doe',
      id: '12345',
      location: event.detailedText ?? event.text,
    );

    // Padding(
    //     padding: const EdgeInsets.only(left: 1),
    //     child: ColoredBox(
    //         color: ColorHelper.thePunchRed(),
    //         child: Padding(
    //           padding: const EdgeInsets.all(2),
    //           child: SizedBox(
    //             height: 60, // Set a fixed height or adjust as needed
    //             child: Column(
    //               crossAxisAlignment: CrossAxisAlignment.start,
    //               children: [

    //                 Expanded(
    //                     child: SingleChildScrollView(
    //                         physics: const NeverScrollableScrollPhysics(),
    //                         child: Text(event.detailedText ?? event.text,
    //                             style: event.textStyle))),
    //               ],
    //             ),
    //           ),
    //         )));
    if (event.onTap != null) {
      child = InkWell(
        onTap: event.onTap,
        child: child,
      );
    }

    if (event.popupItemBuilder != null) {
      child = AlignedPopupMenuButton<int>(
        itemBuilder: event.popupItemBuilder!,
        onSelected: event.popupOnSelected,
        child: child,
      );
    }

    if (event.tooltip != null) {
      child = Tooltip(
          message: event.tooltip,
          textStyle: event.tooltipTextStyle,
          child: child);
    }
    return child;
  }
}
